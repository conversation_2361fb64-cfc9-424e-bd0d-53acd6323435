﻿namespace Lighthouse.Model.ViewModel.MasterData.CargoDescription {
    public class CargoDescriptionModel {
        public Guid CargoDescriptionId { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid CreatedById { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedById { get; set; }
        public string UpdatedByName { get; set; }
        public bool Deleted { get; set; }
        public bool Disabled { get; set; }
        public bool IsAdhoc { get; set; }
    }
}
