﻿namespace Lighthouse.Model.ViewModel.MasterData.Cargo
{
    public class CargoModel
    {
        public Guid CargoId { get; set; }

        public string CreatedByName { get; set; }

        public string UpdatedByName { get; set; }

        public string UnitType { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string CcuId { get; set; }

        public bool IsPool { get; set; }

        public Guid FamilyId { get; set; }

        public string CargoFamilyName { get; set; }

        public Guid SizeId { get; set; }

        public string CargoSizeName { get; set; }

        public Guid? TypeId { get; set; }

        public string CargoTypeName { get; set; }

        [Required]
        public Guid VendorId { get; set; }

        public string VendorVendorName { get; set; }

        public string GroupType {
            get
            {
                return $"{CargoSizeName} {CargoTypeName} {CargoFamilyName}";
            }
        }

        public double Length { get; set; }

        public double Width { get; set; }

        public double LengthFt => Math.Round(UnitsNet.Length.FromMillimeters(Length).Feet, 2);

        public double WidthFt => Math.Round(UnitsNet.Length.FromMillimeters(Width).Feet, 2);

        public double? Height { get; set; }

        public double? HeightFt => Height.HasValue ? Math.Round(UnitsNet.Length.FromMillimeters(Height.Value).Feet, 2) : 0;

        public double? TareMass { get; set; }

        public double MaxGrossWeight { get; set; }

        public bool IsOneOff { get; set; }

        public List<CargoCertificateModel> Certificates { get; set; }

        public DateTime? CertificateExpireDate {
            get
            {
                var latestCertificateDate = Certificates?.OrderByDescending(x => x.CreatedDate).FirstOrDefault();

                if(latestCertificateDate == null)
                {
                    return CertificateTestDate.HasValue ? CertificateTestDate.Value.ToDateTime(TimeOnly.Parse("10:00 PM")).AddMonths(6) : null;
                }
                else
                {
                    return latestCertificateDate.ExpiryDate;
                }
            }
        }

        public Guid LocationId { get; set; }

        public string LocationName { get; set; }

        public Guid CargoDescriptionId { get; set; }

        public string CargoDescriptionName { get; set; }

        public CargoCategory? Category { get; set; }

        public bool IsDeckCargo { get; set; }

        public bool IsApproved { get; set; }

        public DateOnly? CertificateTestDate { get; set; }
        public CargoStatus? CargoStatus { get; set; }
        public CargoHireStatus CcuHireStatus { get; set; }
        
        public Guid? PoolId { get; set; }
        public string PoolName { get; set; }
        public bool Disabled { get; set; }

        [NotMapped]
        public bool CanBeActionedInContainCcuList { get; set; }

        public Guid? HireRequestCargoId { get; set; }

        [NotMapped]
        public bool IsCargoHireCreated { get; set; }
        public bool IsAdhoc { get; set; }
    }
}
