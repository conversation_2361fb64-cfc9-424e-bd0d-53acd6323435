<div class="container">
  <div class="row">
    <div class="d-flex pt-10 align-content-center">
      <b>Add-ons</b>
    </div>
  </div>
  <form [formGroup]="form">
    <div class="row add-ons">
      <div class="col addon-btn">
        <div
          class="image-container"
          [ngClass]="{ active: lightDuesActive(), hover: lightDuesHover }"
          (click)="toggleLightDues()"
          (mouseenter)="lightDuesHover = true"
          (mouseleave)="lightDuesHover = false"
        >
          <img src="assets/lightdues.svg" alt="Light Dues" class="unchecked" />
          <img
            src="assets/lightdues-selected.svg"
            alt="Light Dues"
            class="checked"
          />
          <img
            src="assets/lightdues-hover.svg"
            alt="Light Dues Hover"
            class="hover-image"
          />
        </div>
      </div>
      <div class="col addon-btn">
        <div
          class="image-container"
          [ngClass]="{ active: pilotActive(), hover: pilotHover }"
          (click)="togglePilot()"
          (mouseenter)="pilotHover = true"
          (mouseleave)="pilotHover = false"
        >
          <img src="assets/pilot.svg" alt="Pilot" class="unchecked" />
          <img src="assets/pilot-selected.svg" alt="Pilot" class="checked" />
          <img
            src="assets/pilot-hover.svg"
            alt="Pilot Hover"
            class="hover-image"
          />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col p-3">
        <p-multiSelect
          #multiSelect
          [options]="activityCategoryTypesFlattened"
          formControlName="activityCategoryTypes"
          optionLabel="name"
          optionValue="activityCategoryTypeId"
          placeholder="Add services"
          appendTo="body"
          [filter]="true"
          [group]="true"
          [showToggleAll]="!multiSelect.isEmpty()"
          display="comma"
          (onChange)="onSelectionChange($event.value)"
          [showClear]="!!form.controls.activityCategoryTypes.value?.length"
          styleClass="new-version-multiselect"
          panelStyleClass="new-version-panel"
        ></p-multiSelect>
      </div>
    </div>
  </form>

  <div class="selected-items-container" *ngFor="let item of selectedItems">
    <div class="selected-item">
      <span class="label">{{ item.name }}</span>
      <button
        class="btn-tertiary"
        (click)="
          openTimeDialog(
            item,
            $event,
            item.activityCategoryType.activityCategoryId
          )
        "
      >
        <p class="time-label">{{ item.hours || 0 }} hr</p>
      </button>
      <button class="btn-tertiary" (click)="openNotesDialog(item, $event)">
        <img *ngIf="!item.notes" src="assets/note.svg" alt="note" />
        <img
          *ngIf="item.notes"
          src="assets/notes-indicator.svg"
          class="notepad"
        />
      </button>
      <button pButton class="btn-tertiary" (click)="removeSelectedItem(item)">
        <i class="pi pi-times"></i>
      </button>
    </div>
  </div>
</div>
