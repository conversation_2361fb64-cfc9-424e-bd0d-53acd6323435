﻿namespace Lighthouse.Model.Mapping {
    public class VoyageMaterialDetailMappingProfile : Profile {
        public VoyageMaterialDetailMappingProfile() {

            CreateMap<VoyageMaterialDetail, VoyageMaterialDetailModel>()
                .ForMember(d => d.CreatedByName, s => s.MapFrom(p => p.CreatedBy.Deleted ? "Deleted User" : $"{p.CreatedBy.Firstname} {p.CreatedBy.Lastname}"))
                .ForMember(d => d.UpdatedByName, s => s.MapFrom(p => p.UpdatedById == null ? "" : p.UpdatedBy.Deleted ? "Deleted User" : $"{p.UpdatedBy.Firstname} {p.UpdatedBy.Lastname}"))
                .ForMember(d => d.Weight, s => s.MapFrom(p => p.WeightKg))
                .ForMember(d => d.VendorName, s => s.MapFrom(p => p.Vendor != null ? p.Vendor.VendorName : ""));

            CreateMap<VoyageMaterialDetailUpsertModel, VoyageMaterialDetail>()
                .ForMember(d => d.VoyageMaterialDetailId, p => p.Ignore())
                .ForMember(d => d.WeightKg, s => s.MapFrom(p => p.Weight))
                .ForMember(d => d.VoyageMaterialDetailId, s => s.Ignore())
                .ForMember(d => d.Status, s => s.Ignore());

            CreateMap<VoyageMaterialDetailUpsertModel, VoyageMaterialDetailModel>();
        }
    }
}
