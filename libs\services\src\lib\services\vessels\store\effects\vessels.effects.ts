import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { VesselsService } from '../../vessels.service';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { VesselActions } from '../actions/vessels.actions';
import { Vessel } from '../../interfaces/vessel.interface';
import { switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { FileService } from '../../../file.service';
import { UnitActions } from '../../../maintenance/store/actions/units.actions';

export const loadVessels = createEffect(
  (actions = inject(Actions), service = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.load_Vessels, VesselActions.remove_Vessel_Success),
      mergeMap(() =>
        service.loadVesselList().pipe(
          map((vessels) => VesselActions.load_Vessels_Success({ vessels })),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessels_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselById = createEffect(
  (actions = inject(Actions), service = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.load_Vessel, VesselActions.set_VesselId),
      mergeMap(({ vesselId }) =>
        service.loadVessel(vesselId).pipe(
          map((vessel) => VesselActions.load_Vessel_Success({ vessel })),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadUnits = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(VesselActions.init_vessel_add_edit),
      switchMap(() => of(UnitActions.load_Units()))
    );
  },
  { functional: true }
);

export const removeVessel = createEffect(
  (actions = inject(Actions), vesselsService = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.remove_Vessel),
      mergeMap((action) =>
        vesselsService.removeVessel(action.id).pipe(
          map((res: Vessel) => {
            return VesselActions.remove_Vessel_Success({
              vessel: res,
              successMessage: 'Vessel removed successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.remove_Vessel_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addVessel = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService),
    router = inject(Router),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VesselActions.add_Vessel),
      mergeMap((action) =>
        vesselsService
          .addVessel(
            fileService.createFormData(action.vessel, action.img, [
              'construction',
            ])
          )
          .pipe(
            map((res: Vessel) => {
              router.navigate(['vessels', res.vesselId, 'details']);
              return VesselActions.add_Vessel_Success({
                vessel: res,
                successMessage: 'Vessel added successfully!',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(VesselActions.add_Vessel_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const editVessel = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VesselActions.edit_Vessel),
      mergeMap((action) =>
        vesselsService
          .editVessel(
            action.vesselId,
            fileService.createFormData(action.vessel, action.img, [
              'construction',
            ])
          )
          .pipe(
            map((res: Vessel) =>
              VesselActions.edit_Vessel_Success({
                vessel: res,
                successMessage: 'Vessel edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(VesselActions.edit_Vessel_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselPicture = createEffect(
  (actions = inject(Actions), vesselsService = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.load_Vessel_Picture),
      mergeMap(({ vesselPictureId }) =>
        vesselsService.loadVesselPicture(vesselPictureId).pipe(
          map((vesselPicture: Blob) =>
            VesselActions.load_Vessel_Picture_Success({
              vesselPicture,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Picture_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);
