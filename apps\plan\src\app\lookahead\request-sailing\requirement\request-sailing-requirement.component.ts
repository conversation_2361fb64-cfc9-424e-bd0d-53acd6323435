import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  debounceTime,
  distinctUntilChanged,
  startWith,
  Subscription,
} from 'rxjs';
import { map } from 'rxjs/operators';
import moment from 'moment';
import { greaterThan } from '../../../../../../../libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from '../../../../../../../libs/components/src/lib/validators/decimal-point';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { ActivityCategory } from '../../../../../../../libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { ActivityCategoryType } from '../../../../../../../libs/services/src/lib/services/maintenance/interfaces/activity-category-type.interface';
import { CategoryMethods } from '../../../shared/category-methods';
import { SailingRequestActivity } from '../../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-activity.interface';
import { Unit } from '../../../../../../../libs/services/src/lib/services/interfaces/unit.interface';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { RoleCheckService } from 'libs/services/src/lib/services/shared/role-check.service';
import { RepeatDialogComponent } from '../../repeat-dialog/repeat-dialog.component';
import { SailingRequestActivityStatus } from '../../../../../../../libs/services/src/lib/services/lookahead/enums/sailing-request-activity-status.enum';
import { ClientLocation } from 'libs/services/src/lib/services/client-locations/interfaces/client-locations.interface';
import { minDateValidator } from 'libs/components/src/lib/validators/min-date.validator';
import { CalendarModule } from 'primeng/calendar';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelect, MultiSelectModule } from 'primeng/multiselect';
import { Store } from '@ngrx/store';
import { NotesComponent } from '../../add-request/requirement/notes/notes.component';
import { TimeInputComponent } from '../../add-request/requirement/time-input/time-input.component';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { InputSwitchModule } from 'primeng/inputswitch';
import { DropdownModule } from 'primeng/dropdown';
import { TimeData } from '../../../shared/types/time-data.interface';
import { TimeDialogActions } from '../../../store/actions/time-dialog.actions';
import { MatDialog } from '@angular/material/dialog';
import { notesDialogFeature } from '../../../store/features/notes-dialog.feature';
import { timeDialogFeature } from '../../../store/features/time-dialog.feature';
import { NotesDialogResult } from '../../../shared/types/notes-dialog-result.interface';
import { NotesDialogActions } from '../../../store/actions/notes-dialog.actions';
import { requestPanelFeature } from '../../../../../../../libs/services/src/lib/services/lookahead/store/features/request-panel.feature';

@Component({
  selector: 'request-sailing-requirement',
  templateUrl: './request-sailing-requirement.component.html',
  styleUrls: ['./request-sailing-requirement.component.scss'],
  standalone: true,

  imports: [
    NgIf,
    NgFor,
    ReactiveFormsModule,
    NgClass,
    FormsModule,
    CalendarModule,
    InputTextareaModule,
    MultiSelectModule,
    InputTextModule,
    ToggleButtonModule,
    InputSwitchModule,
    DropdownModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestSailingRequirementComponent implements OnInit, OnChanges {
  @ViewChild('addOnButtons', { static: true })
  addOnButtons!: ElementRef<HTMLDivElement>;
  sharedService = inject(LookaheadSharedService);
  roleCheckService = inject(RoleCheckService);
  store = inject(Store);

  selectedItems: SailingRequestActivity[] = [];
  isBulk: boolean = false;
  cdr = inject(ChangeDetectorRef);
  dialog = inject(MatDialog);

  @Input() units: Unit[] = [];
  @Input() activityCategories: ActivityCategory[] = [];
  @Input() inboundSelected = false;
  @Input() currentUser: User | null = null;
  @Input() originalClients: ClientLocation[] = [];
  @Input() hasCompletedRequests = false;
  @Input() showErrors: boolean = false;
  @Output() sendDataToParent = new EventEmitter<any>();

  activityCategoryTypesFlattened: any[] = [];

  clients: ClientLocation[] = [];
  headerText = 'Single occurrence';
  today = moment().startOf('day').toDate();
  searchClientControl = new FormControl('');
  private subscriptions = new Subscription();
  editMode = false;
  searchServiceControl = new FormControl();
  filteredCategories!: ActivityCategory[];

  form = new FormGroup({
    clientId: new FormControl<string | null>(null, []),
    isFlexableTiming: new FormControl<boolean>(false),
    arrivalTime: new FormControl<Date | null>(null, [Validators.required]),
    endTime: new FormControl<Date | null>(null),
    firstInstallationTime: new FormControl<Date | null>(null),
    latestArrivalTime: new FormControl<Date | null>(null),
    clusterTime: new FormControl<number | null>(null, [
      greaterThan(0),
      decimalPoint(2),
    ]),
    doesRepeat: new FormControl(''),
    timeUnit: new FormControl(''),
    activityCategoryTypes: new FormControl<string[]>([], []),
    isMailbag: new FormControl<boolean>(false),
    isBulkReq: new FormControl<boolean>(false),
    clientReference: new FormControl<string>(''),
    remarks: new FormControl<string>(''),
    seriesStartTime: new FormControl<Date | null>(null),
    seriesEndTime: new FormControl<Date | null>(null),
    weeklyPattern: new FormControl<string>(''),
    repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
  });
  repeatOptions = [
    { label: 'Does Not Repeat', value: 'DoesNotRepeat' },
    { label: 'Does Repeat', value: 'DoesRepeat' },
  ];

  panelSailingRequest = this.store.selectSignal(
    requestPanelFeature.selectPanelSailingRequest
  );
  notesResult = this.store.selectSignal(notesDialogFeature.selectResult);
  timeResult = this.store.selectSignal(timeDialogFeature.selectResult);

  constructor() {
    effect(
      () => {
        const notesResult = this.notesResult();
        if (notesResult && notesResult.notes !== undefined) {
          const serviceToUpdate = this.selectedItems.find(
            (item) => item.activityCategoryTypeId === notesResult.id
          );
          if (serviceToUpdate) {
            this.updateService(serviceToUpdate, { notes: notesResult.notes });
          }
        }

        const timeResult = this.timeResult();

        if (timeResult) {
          const timeServiceToUpdate = this.selectedItems.find(
            (item) => item.activityCategoryTypeId === timeResult.id
          );
          if (timeServiceToUpdate) {
            this.updateService(timeServiceToUpdate, {
              hours: timeResult.hours,
              quantity: timeResult.quantity,
              unitName: timeResult.unitName,
            });
          }
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const request: any = this.panelSailingRequest();
        if (request) {
          const activityCategoryTypes = request.SailingRequestActivities.filter(
            (x: any) => x.isInPort
          ).map((x: any) => x.activityCategoryType);
          this.form
            .get('activityCategoryTypes')
            ?.setValue(
              activityCategoryTypes.map((x: any) => x.activityCategoryTypeId)
            );
          this.selectedItems = request.SailingRequestActivities.filter(
            (x: any) => !x.isInPort
          );
          this.patchForm(request);
        }
      },
      { allowSignalWrites: true }
    );
  }

  private setupDateTimeSubscribers(): void {
    // isFlexableTiming
    this.form.get('isFlexableTiming')?.valueChanges.subscribe((flexValue) => {
      const arrivalValue = this.form.value.arrivalTime;

      if (flexValue) {
        if (arrivalValue) {
          this.form.controls.latestArrivalTime.setValidators([
            minDateValidator(arrivalValue, true),
          ]);
        } else {
          this.form.controls.latestArrivalTime.clearValidators();
        }
        this.form.controls.latestArrivalTime.updateValueAndValidity();

        this.form.controls.firstInstallationTime.clearValidators();
        this.form.controls.firstInstallationTime.updateValueAndValidity();

        const latestValue = this.form.value.latestArrivalTime;
        if (arrivalValue && latestValue) {
          if (
            moment(arrivalValue).isSameOrAfter(moment(latestValue), 'minute')
          ) {
            const controlErrors =
              this.form.controls.latestArrivalTime.errors || {};
            controlErrors['minDate'] = true;
            delete controlErrors['sameTime'];
            this.form.controls.latestArrivalTime.setErrors(controlErrors);
            this.form.controls.latestArrivalTime.markAsTouched();
          } else {
            const controlErrors = this.form.controls.latestArrivalTime.errors;
            if (controlErrors) {
              delete controlErrors['minDate'];
              delete controlErrors['sameTime'];
              this.form.controls.latestArrivalTime.setErrors(
                Object.keys(controlErrors).length ? controlErrors : null
              );
            }
          }
          this.form.controls.latestArrivalTime.updateValueAndValidity();
        }
      } else {
        if (arrivalValue) {
          this.form.controls.firstInstallationTime.setValidators([
            minDateValidator(arrivalValue, true),
          ]);
        } else {
          this.form.controls.firstInstallationTime.clearValidators();
        }
        this.form.controls.firstInstallationTime.updateValueAndValidity();

        this.form.controls.latestArrivalTime.clearValidators();
        this.form.controls.latestArrivalTime.updateValueAndValidity();

        const firstInstallationValue = this.form.value.firstInstallationTime;
        if (arrivalValue && firstInstallationValue) {
          if (
            moment(arrivalValue).isSameOrAfter(
              moment(firstInstallationValue),
              'minute'
            )
          ) {
            const controlErrors =
              this.form.controls.firstInstallationTime.errors || {};
            controlErrors['minDate'] = true;
            delete controlErrors['sameTime'];
            this.form.controls.firstInstallationTime.setErrors(controlErrors);
            this.form.controls.firstInstallationTime.markAsTouched();
          } else {
            const controlErrors =
              this.form.controls.firstInstallationTime.errors;
            if (controlErrors) {
              delete controlErrors['minDate'];
              delete controlErrors['sameTime'];
              this.form.controls.firstInstallationTime.setErrors(
                Object.keys(controlErrors).length ? controlErrors : null
              );
            }
          }
          this.form.controls.firstInstallationTime.updateValueAndValidity();
        }
      }

      this.cdr.detectChanges();
    });

    // Arrival Time
    this.form.get('arrivalTime')?.valueChanges.subscribe((arrivalValue) => {
      if (this.form.value.isFlexableTiming) {
        if (arrivalValue) {
          this.form.controls.latestArrivalTime.setValidators([
            minDateValidator(arrivalValue, true),
          ]);
        } else {
          this.form.controls.latestArrivalTime.clearValidators();
        }
        this.form.controls.latestArrivalTime.updateValueAndValidity();

        this.form.controls.firstInstallationTime.clearValidators();
        this.form.controls.firstInstallationTime.updateValueAndValidity();

        const latestValue = this.form.value.latestArrivalTime;
        if (arrivalValue && latestValue) {
          if (
            moment(arrivalValue).isSameOrAfter(moment(latestValue), 'minute')
          ) {
            const controlErrors =
              this.form.controls.latestArrivalTime.errors || {};
            controlErrors['minDate'] = true;
            delete controlErrors['sameTime'];
            this.form.controls.latestArrivalTime.setErrors(controlErrors);
            this.form.controls.latestArrivalTime.markAsTouched();
          } else {
            const controlErrors = this.form.controls.latestArrivalTime.errors;
            if (controlErrors) {
              delete controlErrors['minDate'];
              delete controlErrors['sameTime'];
              this.form.controls.latestArrivalTime.setErrors(
                Object.keys(controlErrors).length ? controlErrors : null
              );
            }
          }
          this.form.controls.latestArrivalTime.updateValueAndValidity();
        }
      } else {
        if (arrivalValue) {
          this.form.controls.firstInstallationTime.setValidators([
            minDateValidator(arrivalValue, true),
          ]);
        } else {
          this.form.controls.firstInstallationTime.clearValidators();
        }
        this.form.controls.firstInstallationTime.updateValueAndValidity();

        this.form.controls.latestArrivalTime.clearValidators();
        this.form.controls.latestArrivalTime.updateValueAndValidity();

        const firstInstallationValue = this.form.value.firstInstallationTime;
        if (arrivalValue && firstInstallationValue) {
          if (
            moment(arrivalValue).isSameOrAfter(
              moment(firstInstallationValue),
              'minute'
            )
          ) {
            const controlErrors =
              this.form.controls.firstInstallationTime.errors || {};
            controlErrors['minDate'] = true;
            delete controlErrors['sameTime'];
            this.form.controls.firstInstallationTime.setErrors(controlErrors);
            this.form.controls.firstInstallationTime.markAsTouched();
          } else {
            const controlErrors =
              this.form.controls.firstInstallationTime.errors;
            if (controlErrors) {
              delete controlErrors['minDate'];
              delete controlErrors['sameTime'];
              this.form.controls.firstInstallationTime.setErrors(
                Object.keys(controlErrors).length ? controlErrors : null
              );
            }
          }
          this.form.controls.firstInstallationTime.updateValueAndValidity();
        }
      }
      this.cdr.detectChanges();
    });

    // Latest Arrival Time
    this.form
      .get('latestArrivalTime')
      ?.valueChanges.subscribe((latestValue) => {
        const arrivalValue = this.form.value.arrivalTime;
        if (this.form.value.isFlexableTiming && latestValue && arrivalValue) {
          if (
            moment(latestValue).isSameOrBefore(moment(arrivalValue), 'minute')
          ) {
            const controlErrors =
              this.form.controls.latestArrivalTime.errors || {};
            controlErrors['minDate'] = true;
            delete controlErrors['sameTime'];
            this.form.controls.latestArrivalTime.setErrors(controlErrors);
          } else {
            const controlErrors = this.form.controls.latestArrivalTime.errors;
            if (controlErrors) {
              delete controlErrors['minDate'];
              delete controlErrors['sameTime'];
              this.form.controls.latestArrivalTime.setErrors(
                Object.keys(controlErrors).length ? controlErrors : null
              );
            }
          }
        }
      });

    // First Installation Time
    this.form
      .get('firstInstallationTime')
      ?.valueChanges.subscribe((firstInstallationValue) => {
        const arrivalValue = this.form.value.arrivalTime;
        if (
          !this.form.value.isFlexableTiming &&
          firstInstallationValue &&
          arrivalValue
        ) {
          if (
            moment(firstInstallationValue).isSame(
              moment(arrivalValue),
              'minute'
            )
          ) {
            this.form.controls.firstInstallationTime.setErrors({
              sameTime: true,
            });
          } else {
            const errors = this.form.controls.firstInstallationTime.errors;
            if (errors) {
              delete errors['sameTime'];
              this.form.controls.firstInstallationTime.setErrors(
                Object.keys(errors).length ? errors : null
              );
            }
          }
        }
      });
  }

  ngOnInit() {
    this.subscriptions.add(
      this.sharedService.editPoolSailingRequestSubject.subscribe((data) => {
        this.editMode = true;
        if (data.SeriesStartTime !== null && data.SeriesEndTime !== null) {
          this.headerText = 'Series';
          this.form.controls.doesRepeat.setValue('DoesRepeat');
          this.form.controls.seriesStartTime.setValue(data.SeriesStartTime);
          this.form.controls.seriesEndTime.setValue(data.SeriesEndTime);
        }
        this.patchForm(data);
      })
    );
    this.subscriptions.add(
      this.sharedService.resetRequestSailingFormSubject.subscribe(() => {
        this.resetForm();
      })
    );

    this.form.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => {
        this.emitData();
      });

    this.form.get('clusterTime')?.valueChanges.subscribe((value) => {
      if (value && value > 0) {
        this.form.get('timeUnit')?.setValue('hours');
      } else {
        this.form.get('timeUnit')?.setValue('');
      }
    });

    this.setupDateTimeSubscribers();
    this.clientFilters();
  }

  onSelectionChange(selectedIds: string[]) {
    // Remove items that are no longer selected
    this.selectedItems = this.selectedItems.filter((item) =>
      selectedIds.includes(item.activityCategoryTypeId)
    );

    // Add newly selected items
    selectedIds.forEach((id) => {
      if (
        !this.selectedItems.some((item) => item.activityCategoryTypeId === id)
      ) {
        const categoryType = this.findActivityCategoryTypeById(id);
        if (categoryType) {
          this.selectedItems.push(
            this.createSailingRequestActivity(categoryType)
          );
        }
      }
    });

    this.updateFormControl(selectedIds);
    this.cdr.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['originalClients'] &&
      !changes['originalClients'].isFirstChange()
    ) {
      this.updateValidators();
      this.clients = Object.assign([], this.originalClients);
      this.clientFilters();
    }

    if (changes['isBulkReq']) {
      this.updateValidators();
    }

    if (changes['hasCompletedRequests']) {
      this.updateFormControlsDisabledState();
    }

    if (this.activityCategories) {
      this.filteredCategories = this.activityCategories.slice();
      this.prepareActivityCategoryTypesForMultiSelect();
    }
  }

  private prepareActivityCategoryTypesForMultiSelect(): void {
    if (!this.activityCategories) return;

    this.activityCategoryTypesFlattened = this.activityCategories.map(
      (category) => ({
        label: category.name,
        value: category.activityCategoryId,
        items: category.activityCategoryTypes.map((type) => {
          const isDisabled = this.shouldActivityBeDisabled(type);

          return {
            ...type,
            categoryName: category.name,
            isDisabled: isDisabled,
          };
        }),
      })
    );
  }

  shouldActivityBeDisabled(activity: ActivityCategoryType): boolean {
    const isSelected = this.selectedItems.some(
      (item) => item.activityCategoryTypeId === activity.activityCategoryTypeId
    );

    if (!isSelected) {
      return false;
    }

    const selectedActivity = this.selectedItems.find(
      (item) => item.activityCategoryTypeId === activity.activityCategoryTypeId
    );

    if (selectedActivity!.isDisabled) {
      return true;
    }

    return false;
  }

  private patchForm(sailingRequest: any): void {
    this.form.patchValue({
      clientId: sailingRequest.ClientId,
      endTime: sailingRequest.EndTime,
      weeklyPattern: sailingRequest.WeeklyPattern,
      isFlexableTiming: sailingRequest.IsFlexableTiming,
      arrivalTime: sailingRequest.ArrivalTime
        ? new Date(sailingRequest.ArrivalTime)
        : null,
      firstInstallationTime: sailingRequest.FirstInstallationTime
        ? new Date(sailingRequest.FirstInstallationTime)
        : null,
      latestArrivalTime: sailingRequest.LatestArrivalTime
        ? new Date(sailingRequest.LatestArrivalTime)
        : null,
      clusterTime: sailingRequest.ClusterTime,
      timeUnit: sailingRequest.TimeUnit,
      isMailbag: sailingRequest.IsMailbag,
      isBulkReq: sailingRequest.IsBulkReq,
      clientReference: sailingRequest.ClientReference,
      remarks: sailingRequest.Remarks,
    });
    const activityCategoryTypes = sailingRequest.SailingRequestActivities.map(
      (x: any) => x.activityCategoryType
    );
    this.form
      .get('activityCategoryTypes')
      ?.setValue(
        activityCategoryTypes.map((x: any) => x.activityCategoryTypeId)
      );
    this.selectedItems = sailingRequest.SailingRequestActivities;
    this.updateFormControlsDisabledState();
  }

  notesClicked(service: SailingRequestActivity, event: Event): void {
    event.stopPropagation();

    const notesDate: NotesDialogResult = {
      notes: service.notes || '',
      id: service.activityCategoryTypeId,
    };
    this.store.dispatch(
      NotesDialogActions.openNotesDialog({ data: notesDate })
    );
  }

  timeClicked(service: SailingRequestActivity, event: Event, id: string): void {
    event.stopPropagation();

    let activityCategory = this.findActivityCategory(
      service.activityCategoryTypeId
    );
    if (
      activityCategory.activityType === 0 ||
      activityCategory.activityType === 1
    ) {
      this.isBulk = true;
    }

    let dialogData = {
      hours: service.hours || 0,
      quantity: service.quantity || 0,
      unitName: service.unitName,
      units: this.units || [],
      isBulk: this.isBulk,
      id: service.activityCategoryTypeId,
    } as TimeData;

    this.store.dispatch(
      TimeDialogActions.change_Visibility_Time_Dialog({
        visible: true,
        data: dialogData,
      })
    );
  }

  findActivityCategory(id: string): ActivityCategory {
    const categories = this.activityCategories;

    const found = categories!.find((category: ActivityCategory) => {
      const hasMatchingType = category.activityCategoryTypes.some(
        (item: ActivityCategoryType) => {
          return item.activityCategoryTypeId === id;
        }
      );
      return hasMatchingType;
    });
    return found!;
  }

  clientFilters(): void {
    this.searchClientControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterClients(value!))
      )
      .subscribe((filteredClients) => {
        this.clients = filteredClients;
      });
    this.cdr.detectChanges();
  }

  private _filterClients(value: string): ClientLocation[] {
    if (value === '') {
      return this.originalClients ?? [];
    }

    const filterValue = value.toLowerCase();
    const numericFilterValue = value.toString().toLowerCase();

    return this.originalClients!.filter(
      (client) =>
        client.clientName.toLowerCase().includes(filterValue) ||
        client.clientName.toLowerCase().includes(numericFilterValue)
    );
  }

  findActivityCategoryTypeById(id: string): ActivityCategoryType | undefined {
    for (const group of this.activityCategoryTypesFlattened) {
      const found = group.items.find(
        (item: ActivityCategoryType) => item.activityCategoryTypeId === id
      );
      if (found) return found;
    }
    return undefined;
  }

  createSailingRequestActivity(
    item: ActivityCategoryType
  ): SailingRequestActivity {
    return {
      sailingRequestActivityId: '',
      sailingRequestId: '',
      activityCategoryTypeId: item.activityCategoryTypeId,
      activityCategoryType: item,
      hours: 0,
      notes: '',
      name: item.name,
      quantity: 0,
      unitName: null,
      area: null,
      asset: null,
      areaId: null,
      assetId: null,
      startTime: null,
      endTime: null,
      status: 0,
      activityCategoryTypeActivityCategoryActivityType:
        item.activityCategory?.activityType || null,
      dependantActivityId: null,
    };
  }

  selectItem(item: ActivityCategoryType): void {
    const newActivity: SailingRequestActivity = {
      sailingRequestActivityId: '',
      sailingRequestId: '',
      activityCategoryTypeId: item.activityCategoryTypeId,
      activityCategoryType: item,
      hours: 0,
      notes: '',
      name: item.name,
      quantity: 0,
      unitName: null,
      area: null,
      asset: null,
      areaId: null,
      assetId: null,
      startTime: null,
      endTime: null,
      status: 0,
      activityCategoryTypeActivityCategoryActivityType:
        item.activityCategory?.activityType || null,
      dependantActivityId: null,
    };
    if (!this.isSelected(item)) {
      this.selectedItems.push(newActivity);
      this.emitData();
      this.cdr.detectChanges();
    } else {
      this.deselectItem(newActivity);
    }
  }

  isSelected(item: ActivityCategoryType): boolean {
    return this.selectedItems.some(
      (selected) =>
        selected.activityCategoryTypeId === item.activityCategoryTypeId
    );
  }

  deselectItem(item: SailingRequestActivity): void {
    const index = this.selectedItems.findIndex(
      (selected) =>
        selected.activityCategoryTypeId === item.activityCategoryTypeId
    );
    if (index !== -1) {
      this.selectedItems.splice(index, 1);
      this.emitData();
      this.cdr.detectChanges();
    }
  }

  updateService(
    service: SailingRequestActivity,
    updates: Partial<SailingRequestActivity>
  ): void {
    const updatedService = { ...service, ...updates };
    const index = this.selectedItems.findIndex(
      (item) => item.activityCategoryTypeId === service.activityCategoryTypeId
    );
    if (index !== -1) {
      this.selectedItems[index] = updatedService;
      this.emitData();
    }
    this.cdr.detectChanges();
  }

  private transformToSailingRequestActivities(): SailingRequestActivity[] {
    return this.selectedItems.map((item) => ({
      sailingRequestActivityId: '',
      sailingRequestId: '',
      activityCategoryTypeId: item.activityCategoryTypeId,
      activityCategoryType: item.activityCategoryType,
      hours: item.hours || 0,
      notes: item.notes || '',
      name: item.name,
      quantity: item.quantity,
      unitName: item.unitName,
      area: null,
      areaId: null,
      assetId: null,
      asset: null,
      startTime: null,
      endTime: null,
      status: SailingRequestActivityStatus.Unplanned,
      activityCategoryTypeActivityCategoryActivityType:
        item.activityCategoryTypeActivityCategoryActivityType,
      sailingRequest: null,
      dependantActivityId: null,
    }));
  }

  openRepeatDialog() {
    const dialogRef = this.dialog.open(RepeatDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        startDate:
          this.form.value.seriesStartTime !== null
            ? this.form.value.seriesStartTime
            : this.form.value.arrivalTime !== null
            ? this.form.value.arrivalTime
            : null,
        endDate:
          this.form.value.seriesEndTime !== null
            ? this.form.value.seriesEndTime
            : new Date(new Date().getFullYear(), 11, 31),
        weeklyPattern: this.form.value.weeklyPattern,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (!this.editMode) {
          this.form.get('arrivalTime')?.setValue(result.startDate);
          this.form.get('endTime')?.setValue(result.endDate);
        }
        this.form.get('seriesStartTime')?.setValue(result.startDate);
        this.form.get('seriesEndTime')?.setValue(result.endDate);
        this.form.get('weeklyPattern')?.setValue(result.weeklyPattern);
        this.form
          .get('repeatEveryNumberOfWeeks')
          ?.setValue(result.repeatEveryNumberOfWeeks);
        this.emitData();
      } else {
        if (
          this.form.value.seriesStartTime === null &&
          this.form.value.seriesEndTime === null
        ) {
          this.form.get('doesRepeat')?.setValue('');
        }
      }
    });
  }

  emitData() {
    if (this.form.valid) {
      const formData = this.form.value;
      const sailingRequestActivities =
        this.transformToSailingRequestActivities();
      this.sendDataToParent.emit({ formData, sailingRequestActivities });
    }

    this.updateFormControlsDisabledState();
  }

  hasOnlyCliRoleInPlan(): boolean {
    return this.roleCheckService.hasOnlyCliRoleInPlan(
      this.currentUser || undefined
    );
  }

  convertMomentToDate(date: any): Date | null {
    if (moment.isMoment(date)) {
      return date.toDate();
    }
    return date;
  }

  setDoesRepeat(value: string) {
    this.headerText = value === 'DoesRepeat' ? 'Series' : 'Single occurrence';
    this.form.get('doesRepeat')?.setValue(value);
    if (value === 'DoesRepeat') {
      this.openRepeatDialog();
    } else {
      this.form.get('seriesStartTime')?.setValue(null);
      this.form.get('seriesEndTime')?.setValue(null);
      this.form.get('weeklyPattern')?.setValue('');
      this.form.get('repeatEveryNumberOfWeeks')?.setValue(null);
    }
  }

  @ViewChild('multiSelect') multiSelect!: MultiSelect;

  removeSelectedItem(item: SailingRequestActivity) {
    this.selectedItems = this.selectedItems.filter(
      (selected) =>
        selected.activityCategoryTypeId !== item.activityCategoryTypeId
    );

    const currentValue = this.form.get('activityCategoryTypes')!
      .value as string[];
    const updatedValue = currentValue.filter(
      (id) => id !== item.activityCategoryTypeId
    );

    this.updateFormControl(updatedValue);

    // Force update the MultiSelect
    if (this.multiSelect) {
      this.multiSelect.updateModel(updatedValue);
    }

    this.cdr.detectChanges();
  }

  updateFormControl(value: string[]) {
    this.form.get('activityCategoryTypes')!.setValue(value);
  }

  toggleBulkReq() {
    this.updateValidators();
    this.updateFormControlsDisabledState();
  }

  private updateFormControlsDisabledState(): void {
    if (this.hasCompletedRequests) {
      this.form.get('clientId')?.disable({ emitEvent: false });
      this.form.get('arrivalTime')?.disable({ emitEvent: false });
      this.form.get('doesRepeat')?.disable({ emitEvent: false });

      if (this.form.get('isBulkReq')?.value) {
        this.form.get('isBulkReq')?.disable({ emitEvent: false });
      } else {
        this.form.get('isBulkReq')?.enable({ emitEvent: false });
      }
    } else {
      this.form.get('clientId')?.enable({ emitEvent: false });
      this.form.get('arrivalTime')?.enable({ emitEvent: false });
      this.form.get('doesRepeat')?.enable({ emitEvent: false });

      this.form.get('isBulkReq')?.enable({ emitEvent: false });
    }
  }

  updateValidators() {
    if (!this.hasOnlyCliRoleInPlan()) {
      const clientIdControl = this.form.get('clientId');
      clientIdControl?.setValidators([Validators.required]);
      clientIdControl?.updateValueAndValidity();
    }
    const activityCategoryTypesControl = this.form.get('activityCategoryTypes');
    if (this.form.get('isBulkReq')?.value === true) {
      activityCategoryTypesControl?.setValidators([Validators.required]);
    } else {
      activityCategoryTypesControl?.clearValidators();
    }
    activityCategoryTypesControl?.updateValueAndValidity();
  }

  resetForm() {
    this.form.reset({
      isFlexableTiming: false,
      arrivalTime: null,
      clusterTime: null,
      firstInstallationTime: null,
      latestArrivalTime: null,
      doesRepeat: '',
      timeUnit: '',
      activityCategoryTypes: [],
      isMailbag: false,
      isBulkReq: false,
      clientReference: '',
      remarks: '',
    });
    this.selectedItems = [];
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.editMode = false;
    this.hasCompletedRequests = false;
    this.form.get('clientId')?.enable({ emitEvent: false });
    this.form.get('arrivalTime')?.enable({ emitEvent: false });
    this.form.get('isBulkReq')?.enable({ emitEvent: false });
    this.form.get('doesRepeat')?.enable({ emitEvent: false });
  }
}
