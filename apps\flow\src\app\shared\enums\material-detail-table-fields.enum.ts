export enum MaterialDetailTableFields {
  status = 'status',
  rowNumber = 'rowNumber',
  assetId = 'assetId',
  voyageCargoId = 'voyageCargoId',
  voyageCargoBulkId = 'voyageCargoBulkId',
  quantity = 'quantity',
  packingUnit = 'packingUnit',
  materialDescription = 'materialDescription',
  whsStatus = 'whsStatus',
  poNo = 'poNo',
  vendorId = 'vendorId',
  vendorName = 'vendorName',
  requester = 'requester',
  whsLocation = 'whsLocation',
  destinationLocation = 'destinationLocation',
  collectDate = 'collectDate',
  collectTime = 'collectTime',
  phone = 'phone',
  transportRequest = 'transportRequest',
  poTransport = 'poTransport',
  comments = 'comments',
  customStatus = 'customStatus',
  customsEntryType = 'customsEntryType',
  documentNo = 'documentNo',
  serialNo = 'serialNo',
  weight = 'weight',
  manifestNo = 'manifestNo',
  value = 'value',
  category = 'category',
  properShippingName = 'properShippingName',
  packingGroup = 'packingGroup',
  imoCode = 'imoCode',
  dangerousGoodId = 'dangerousGoodId',
  unNo = 'unNo',
  class = 'class',
  subClass = 'subClass',
  ltdQuantity = 'ltdQuantity',
  marinePollutant = 'marinePollutant',
  coo = 'coo',
  commodityCode = 'commodityCode',
  jobCardNumber = 'jobCardNumber',
  workOrderNumber = 'workOrderNumber',
  mivmmt = 'mivmmt',
  currency = 'currency',
  voyageCargoCcuId = 'voyageCargoCcuId',
  voyageCargoBulkBulkTypeName = 'voyageCargoBulkBulkTypeName',
  voyageMaterialDetailDangerousGoodId = 'voyageMaterialDetailDangerousGoodId',
}
