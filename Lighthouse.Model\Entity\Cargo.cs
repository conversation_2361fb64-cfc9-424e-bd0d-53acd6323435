﻿namespace Lighthouse.Model.Entity
{
    public class Cargo
    {
        public Cargo()
        {
            CargoId = Guid.NewGuid();
        }
        public Guid CargoId { get; set; }

        public bool Deleted { get; set; }

        public Guid CreatedById { get; set; }

        public User CreatedBy { get; set; }

        public Guid? UpdatedById { get; set; }

        public User UpdatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string CCUId { get; set; }

        public bool IsPool { get; set; }

        public CargoFamily CargoFamily { get; set; }

        public Guid? FamilyId { get; set; }

        public CargoSize CargoSize { get; set; }

        public Guid? SizeId { get; set; }

        public CargoType CargoType { get; set; }

        public Guid? TypeId { get; set; }

        public Vendor Vendor { get; set; }

        public Guid? VendorId { get; set; }

        public double LengthMm { get; set; }

        public double WidthMm { get; set; }

        public double? HeightMm { get; set; }

        public double? TareMassKg { get; set; }

        public double? MaxGrossWeightKg { get; set; }

        public Location Location { get; set; }

        public Guid? LocationId { get; set; }

        public CargoCategory? Category { get; set; }

        public bool IsDeckCargo { get; set; }

        public bool IsApproved { get; set; } = false;

        public DateOnly? CertificateTestDate { get; set; }

        public Guid CargoDescriptionId { get; set; }

        public CargoDescription CargoDescription { get; set; }

        public CargoStatus? CargoStatus { get; set; }

        public CargoHireStatus CcuHireStatus { get; set; }

        public Guid? PoolId { get; set; }

        public Pool Pool { get; set; }

        public ICollection<VoyageCargo> VoyageCargoes { get; set; }

        public ICollection<CargoCertificate> CargoCertificates { get; set; }
        public bool Disabled { get; set; }
        public bool IsAdhoc { get; set; } = false;
    }
}
