import { VesselsState } from './vessels-state.interface';
import { HireStatement } from './hire-statement.interface';
import { HireStatementBulk } from './hire-statement-bulk.interface';

export interface HireStatementState extends VesselsState {
  hireStatements: HireStatement[];
  hireStatement: HireStatement | null;
  isVisibleAddEditHireStatement: boolean;
  hireStatementBulks: HireStatementBulk[];
  hireStatementBulk: HireStatementBulk | null;
  isVisibleAddEditHireStatementBulk: boolean;
  loading: {
    list: boolean;
    createEdit: boolean;
    bulkList: boolean;
    export: boolean;
    exportBulks: boolean;
  };
}
