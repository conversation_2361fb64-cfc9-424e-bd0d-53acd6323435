import { FieldType } from 'libs/components/src/lib/enums';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { MaterialDetailTableFields } from '../../enums';

export function InitializeMaterialDetailTableView(): ColumnModel[] {
  const calculationListColumns = [
    new ColumnModel(MaterialDetailTableFields.rowNumber, 'No', 60),
    new ColumnModel(MaterialDetailTableFields.status, 'Status', 60, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(MaterialDetailTableFields.assetId, 'Asset', 200, {
      fieldType: FieldType.dropdown,
      validator: true,
    }),
    new ColumnModel(MaterialDetailTableFields.voyageCargoCcuId, 'Container', 200, {
      fieldType: FieldType.text,
      validator: true,
    }),
    new ColumnModel(MaterialDetailTableFields.voyageCargoBulkBulkTypeName, 'Bulk', 200, {
      fieldType: FieldType.text,
      validator: true,
    }),
    new ColumnModel(MaterialDetailTableFields.quantity, 'Quantity ', 100, {
      fieldType: FieldType.number,
      decimals: 2,
    }),
    new ColumnModel(MaterialDetailTableFields.packingUnit, 'Packing Unit', 120),
    new ColumnModel(
      MaterialDetailTableFields.materialDescription,
      'Material Description',
      120
    ),
    new ColumnModel(MaterialDetailTableFields.whsStatus, 'WHS Status', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(MaterialDetailTableFields.poNo, 'PO Number', 120),
    new ColumnModel(MaterialDetailTableFields.vendorName, 'Vendor', 200, {
      fieldType: FieldType.text,
    }),
    new ColumnModel(MaterialDetailTableFields.requester, 'Requester', 120),
    new ColumnModel(MaterialDetailTableFields.whsLocation, 'WHS Location', 120),
    new ColumnModel(
      MaterialDetailTableFields.destinationLocation,
      'Destination Location',
      120
    ),
    new ColumnModel(
      MaterialDetailTableFields.collectDate,
      'Collection Date',
      200,
      {
        fieldType: FieldType.datePicker,
      }
    ),
    new ColumnModel(
      MaterialDetailTableFields.collectTime,
      'Collection Time',
      200,
      {
        fieldType: FieldType.timePicker,
      }
    ),
    new ColumnModel(MaterialDetailTableFields.phone, 'Phone Number', 120),
    new ColumnModel(
      MaterialDetailTableFields.transportRequest,
      'Transport Request',
      200,
      {
        fieldType: FieldType.dropdown,
      }
    ),
    new ColumnModel(MaterialDetailTableFields.poTransport, 'PO Transport', 120),
    new ColumnModel(MaterialDetailTableFields.comments, 'Comments', 220),
    new ColumnModel(
      MaterialDetailTableFields.customStatus,
      'Customs Status',
      150,
      {
        fieldType: FieldType.dropdown,
      }
    ),
    new ColumnModel(
      MaterialDetailTableFields.customsEntryType,
      'Customs Declaration type',
      150,
      {
        fieldType: FieldType.dropdown,
      }
    ),
    new ColumnModel(
      MaterialDetailTableFields.documentNo,
      'Document Number',
      120
    ),
    new ColumnModel(MaterialDetailTableFields.serialNo, 'Serial Number', 120),
    new ColumnModel(MaterialDetailTableFields.weight, 'Weight', 100, {
      fieldType: FieldType.number,
      decimals: 2,
    }),
    new ColumnModel(
      MaterialDetailTableFields.manifestNo,
      'Manifest Number',
      120
    ),
    new ColumnModel(MaterialDetailTableFields.value, 'Value', 120),
    new ColumnModel(MaterialDetailTableFields.category, 'Category', 120),
    new ColumnModel(
      MaterialDetailTableFields.properShippingName,
      'Proper Shipping Name',
      120
    ),
    new ColumnModel(
      MaterialDetailTableFields.packingGroup,
      'Packing Group',
      200,
      {
        fieldType: FieldType.dropdown,
      }
    ),
    new ColumnModel(MaterialDetailTableFields.imoCode, 'IMO Code', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(
      MaterialDetailTableFields.voyageMaterialDetailDangerousGoodId,
      'Dangerous Good',
      50,
    ),
    new ColumnModel(MaterialDetailTableFields.unNo, 'UnNo', 200),
    new ColumnModel(MaterialDetailTableFields.class, 'Class', 200),
    new ColumnModel(MaterialDetailTableFields.subClass, 'Sub-class', 200),
    new ColumnModel(
      MaterialDetailTableFields.ltdQuantity,
      'Limited Quantity',
      200,
      {
        fieldType: FieldType.checkbox,
      }
    ),
    new ColumnModel(
      MaterialDetailTableFields.marinePollutant,
      'Marine Pollutant',
      200,
      {
        fieldType: FieldType.checkbox,
      }
    ),
    new ColumnModel(MaterialDetailTableFields.coo, 'Country Of Origin', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(
      MaterialDetailTableFields.commodityCode,
      'Commodity Code',
      200
    ),
    new ColumnModel(
      MaterialDetailTableFields.jobCardNumber,
      'Job Card Number',
      200
    ),
    new ColumnModel(
      MaterialDetailTableFields.workOrderNumber,
      'Work Order Number',
      200
    ),
    new ColumnModel(MaterialDetailTableFields.mivmmt, 'MIV/MMT', 200),
    new ColumnModel(MaterialDetailTableFields.currency, 'Currency', 200, {
      fieldType: FieldType.dropdown,
    }),
  ];
  return calculationListColumns;
}
