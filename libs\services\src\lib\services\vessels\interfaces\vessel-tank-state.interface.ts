import { VesselsState } from './vessels-state.interface';
import { VesselTank } from './vessel-tank.interface';
import { BulkType } from '../../maintenance/interfaces/bulk-type.interface';
import { TankType } from '../../maintenance/interfaces/tank-type.interface';

export interface VesselTankState extends VesselsState {
  vesselTanks: VesselTank[];
  bulkTypes: BulkType[];
  tankTypes: TankType[];
  isVisibleAddEdit: boolean;
  vesselTank: VesselTank | null;
}
