<div class="voyage">
  <div class="table__view">
    <h2 class="table__title">Voyages</h2>
  </div>

  <div [formGroup]="dateRangeControl">
    <div class="table__content">
      <p-table
        #table
        [columns]="columns"
        [paginator]="true"
        [loading]="voyagesState().loading.list"
        [value]="voyagesState().voyages"
        [rows]="10"
        [rowsPerPageOptions]="[5, 10, 20, 50]"
        paginatorDropdownAppendTo="body"
        scrollHeight="550px"
        [scrollable]="true"
        [globalFilterFields]="[
          'voyageNumber',
          'displayVesselName',
          'voyageTypeName'
        ]"
      >
        <ng-template pTemplate="caption">
          <div
            class="d-flex justify-content-end gap-8 align-items-center flex-wrap pb-20"
          >
            <div>
              <p-calendar
                id="startDate"
                formControlName="startDate"
                [showIcon]="true"
                [showClear]="true"
                (onClear)="
                  controls.startDate?.setValue(null);
                  setFilters();
                  loadVoyages()
                "
                [maxDate]="controls.endDate?.value!"
                placeholder="Start Date"
                dateFormat="dd/mm/yy"
                (onSelect)="setFilters(); loadVoyages()"
                appendTo="body"
              ></p-calendar>
            </div>
            <div>
              <p-calendar
                id="endDate"
                formControlName="endDate"
                [showIcon]="true"
                [showClear]="true"
                (onClear)="
                  controls.endDate?.setValue(null); setFilters(); loadVoyages()
                "
                [minDate]="controls.startDate?.value!"
                placeholder="End Date"
                dateFormat="dd/mm/yy"
                (onSelect)="setFilters(); loadVoyages()"
                appendTo="body"
              ></p-calendar>
            </div>
            <span class="p-input-icon-left">
              <em class="pi pi-search"></em>
              <input
                type="text"
                pInputText
                formControlName="search"
                (input)="applyGlobalFilter($event, table, false)"
                placeholder="Search..."
              />
            </span>
            <button
              class="btn-secondary"
              type="button"
              (click)="addEditVoyage(null, false)"
            >
              Add Voyage
            </button>
            <button
              class="btn-secondary"
              type="button"
              (click)="exportTemplate()"
            >
              Export Template
            </button>
            <button
              class="btn-secondary import_btn"
              type="button"
              (click)="fileInput.click()"
              [disabled]="voyagesState().loading.voyageImport"
            >
              <ng-container
                *ngIf="voyagesState().loading.voyageImport; else importText"
              >
                <p-progressSpinner
                  [styleClass]="'small-spinner-style'"
                ></p-progressSpinner>
              </ng-container>
              <ng-template #importText>Import</ng-template>
              <input
                #fileInput
                type="file"
                accept=".xlsx"
                (change)="importFromTemplate($event)"
              />
            </button>
            <button
              class="btn-secondary"
              type="button"
              (click)="exportVoyages()"
            >
              Export
            </button>
          </div>
        </ng-template>
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th *ngFor="let column of columns" scope="col">
              <span>{{ column.name }}</span>
            </th>
          </tr>
        </ng-template>

        <ng-template let-index="rowIndex" pTemplate="body" let-voyage>
          <tr>
            <td>
              <i
                class="pi pi-exclamation-circle invalid-icon"
                *ngIf="voyage.voyageNotValid"
              ></i>
            </td>
            <td>
              <a class="table-link" [routerLink]="[voyage.voyageId]">
                {{ voyage.voyageNumber }}
              </a>
            </td>
            <td>{{ voyage.displayVesselName }}</td>
            <td>
              <lha-custom-chip
                [text]="voyage.voyageTypeName"
                [cssClass]="voyage.voyageTypeName"
              >
              </lha-custom-chip>
            </td>
            <td>
              {{ voyage.voyageStartDateTime | date : 'dd/MM/yyyy HH:mm' }}
            </td>
            <td>{{ voyage.voyageEndDateTime | date : 'dd/MM/yyyy HH:mm' }}</td>
            <td>{{ voyage.initialAssetName }}</td>
            <td>{{ voyage.totalMileage }}</td>
            <td>{{ voyage.deckPercentageUsedIn }}</td>
            <td>{{ voyage.deckPercentageUsedOut }}</td>
            <td>
              <lha-custom-chip
                [text]="voyage.isCompleted ? 'Completed' : 'In Progress'"
              >
              </lha-custom-chip>
            </td>
            <td>
              <div *ngIf="!voyage.isVoyageLocked">
                <i
                  class="pi pi-pencil"
                  (click)="addEditVoyage(voyage, true)"
                ></i>
                <i
                  class="ml-10"
                  [ngStyle]="
                    voyage.isCompleted ? { color: 'red' } : { color: 'green' }
                  "
                  [ngClass]="voyage.isCompleted ? 'pi pi-times' : 'pi pi-check'"
                  (click)="completeIncompleteVoyage(voyage)"
                ></i>
              </div>
              <div *ngIf="voyage.isVoyageLocked">
                <i
                  class="pi pi-lock"
                  [ngStyle]="{ color: 'red' }"
                  title="Voyage is locked"
                ></i>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>

<lha-voyage-add-edit></lha-voyage-add-edit>
