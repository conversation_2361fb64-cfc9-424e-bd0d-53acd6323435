<ng-container *ngIf="voyageData() && !loadings().cargoList; else loading">
  <p-breadcrumb [model]="breadcrumb()" styleClass="mb-4">
    <ng-template pTemplate="separator">/</ng-template>
  </p-breadcrumb>

  <p-card>
    <div class="d-flex align-items-center">
      <span class="shipIconContainer mr-15">
        <img
          [src]="voyageData()?.voyageDirection === voyageDirection.Inbound ? 'assets/inbound.svg': 'assets/outbound.svg'"
        />
      </span>
      <span class="fs-32 f-bold">
        <span class="color-primary">{{ voyageData()?.vesselName }}</span>
        - {{ voyageData()?.voyageDirection === voyageDirection.Inbound ? 'Vessel Discharge' : 'Vessel Load Out' }}
      </span>
    </div>
    <div class="mt-20 d-flex justify-content-between align-items-center">
      <div [style.width.px]="325">
        <p-dropdown
          [options]="assets()"
          [formControl]="assetsFilterControl"
          (onChange)="setFilter()"
          appendTo="body"
          styleClass="new-version"
          panelStyleClass="new-version-panel"
        />
      </div>

      <div class="d-flex gap-8">
        <p-menu #deckPlan [model]="deckPlanItems()" [popup]="true" />

        <button
          *ngIf="voyageData()?.voyageDirection === voyageDirection.Outbound"
          type="button"
          class="btn-secondary d-flex align-items-center gap-8"
          (click)="deckPlan.toggle($event)"
        >
          <span>Deck plan</span>
          <i class="pi pi-angle-down"></i>
        </button>

        <p-menu #discrepancy [model]="discrepancyMenuItems()" [popup]="true" />
        <button
          *ngIf="voyageData()?.voyageDirection === voyageDirection.Inbound"
          class="btn-secondary d-flex align-items-center gap-8"
          type="button"
          (click)="discrepancy.toggle($event)"
        >
          <span>Discrepancy Report</span>
          <i class="pi pi-angle-down"></i>
        </button>

        <button
          *ngIf="canManage && emailButtonVisible()"
          class="btn-secondary"
          type="button"
          (click)="sendDepartureEmail()"
        >
          Send Departure Email
        </button>

        <button
          type="button"
          class="btn-export align-items-center d-flex"
          (click)="export()"
        >
          <img src="assets/exel.svg" />
          Export Excel File
        </button>

        <button
          class="btn-secondary mr-4"
          type="button"
          type="button"
          (click)="dialogToggle()"
        >
          <div class="d-flex align-items-center">Attachments</div>
        </button>
      </div>
    </div>

    <p-table
      [columns]="columns()"
      [value]="cargoList()"
      [scrollable]="true"
      [lazy]="true"
      scrollHeight="400px"
      class="mt-16"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th
            *ngFor="let column of columns"
            scope="col"
            [style.min-width.px]="column.width"
            [style.width.%]="(column.width / tableWidth) * 100"
          >
            <span>{{ column.name }}</span>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-columns="columns" let-rowData>
        <tr>
          <ng-container *ngFor="let column of columns">
            <ng-container [ngSwitch]="column.field">
              <td *ngSwitchCase="tableFields.customsApprovedToLoad">
                {{ rowData[column.field] === customStatusEnum.Yes ? 'Approved to load' : 'Not Applicable' }}
              </td>
              <td *ngSwitchCase="tableFields.isDispatched">
                {{ rowData[column.field] ? 'Yes' : 'No' }}
              </td>
              <td *ngSwitchCase="tableFields.type">
                <div class="d-flex align-items-center">
                  <span *ngIf="rowData.hasDangerousGoods" class="d-flex">
                    <img
                      width="25"
                      src="assets/danger.svg"
                      alt="Dangerous Good"
                    />
                  </span>
                  <span *ngIf="rowData.isHeavy">
                    <img width="25" src="assets/heavy.svg" />
                  </span>
                  <span *ngIf="rowData.isHighPriority">
                    <img
                      src="assets/check.svg"
                      [style.height.px]="24"
                      [style.width.px]="24"
                      class="mr-8"
                    />
                  </span>
                </div>
              </td>
              <td *ngSwitchCase="tableFields.rtRob">
                {{ rtRob[rowData.rtRob] }}
              </td>
              <td *ngSwitchDefault>{{ rowData[column.field] }}</td>
            </ng-container>
          </ng-container>
        </tr>
      </ng-template>
    </p-table>
  </p-card>
</ng-container>

<ng-template #loading>
  <lha-loader [loading]="true" [minHeight]="700" [diameter]="60" />
</ng-template>

<attach-files-dialog
  [entityId]="voyageData()?.voyageId || ''"
  [dialogVisible]="isOpenAttachFilesDialog"
  (dialogToggle)="isOpenAttachFilesDialog = false"
  [attachments]="attachments() || []"
  [viewOnly]="!canManage"
  [withSave]="false"
  [downloadFileFn]="downloadSelectedFile"
  [uploadFileBulkFn]="uploadFileBulk"
  [bulkUpload]="true"
  [removeFileFn]="removeFile"
  [gridTemplateColumns]="'1fr'"
  [filesAccept]="'.pdf,.doc,.docx,.xls,.xlsx,.jpg,.png,.gif,.mp4,.txt'"
  [isLoading]="attachmentsLoading().flowVoyageAttachmentsLists || attachmentsLoading().uploadFileLoading"
  [maxFileCount]="10 - (attachments()!.length || 0)"
  [attachmentTypes]="this.attachmentTypes() || []"
/>

<ng-container *ngIf="voyageData()?.voyageId">
  <app-departure-email-dialog
    [voyageId]="voyageData()?.voyageId"
  ></app-departure-email-dialog>
</ng-container>

<ng-container *ngIf="voyageData()?.voyageId">
  <app-discrepancy-report-email-dialog
    [voyageId]="voyageData()?.voyageId"
  ></app-discrepancy-report-email-dialog>
</ng-container>
