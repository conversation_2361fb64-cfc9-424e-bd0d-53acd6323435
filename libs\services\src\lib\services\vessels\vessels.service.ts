import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Vessel } from './interfaces/vessel.interface';
import { Unit } from '../interfaces/unit.interface';

@Injectable({
  providedIn: 'root',
})
export class VesselsService {
  private http = inject(HttpClient);

  loadVesselList(): Observable<Vessel[]> {
    return this.http.get<Vessel[]>('/api/vessel');
  }

  loadVessel(id: string): Observable<Vessel> {
    return this.http.get<Vessel>(`/api/vessel/${id}`);
  }

  loadVesselUnits(): Observable<Unit[]> {
    return this.http.get<Unit[]>('/api/unit');
  }

  removeVessel(id: string): Observable<Vessel> {
    return this.http.delete<Vessel>(`/api/vessel/${id}`);
  }

  addVessel(vessel: FormData): Observable<Vessel> {
    return this.http.post<Vessel>('/api/vessel', vessel);
  }

  editVessel(id: string, vessel: FormData): Observable<Vessel> {
    return this.http.put<Vessel>(`/api/vessel/${id}`, vessel);
  }

  loadVesselPicture(vesselPictureId: string) {
    return this.http.get(`/api/vessel/photo/${vesselPictureId}`, {
      responseType: 'blob',
    });
  }
}
