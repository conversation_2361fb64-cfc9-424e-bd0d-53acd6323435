﻿namespace Lighthouse.Model.Mapping {
    public class VoyageCargoMappingProfile : Profile {

        public VoyageCargoMappingProfile() {
            CreateMap<VoyageCargo, VoyageCargoModel>()
                .ForMember(d => d.CreatedByName,
                    s => s.MapFrom(p =>
                        p.CreatedBy.Deleted ? "Deleted User" : $"{p.CreatedBy.Firstname} {p.CreatedBy.Lastname}"))
                .ForMember(d => d.UpdatedByName,
                    s => s.MapFrom(p =>
                        p.UpdatedById == null ? "" :
                        p.UpdatedBy.Deleted ? "Deleted User" : $"{p.UpdatedBy.Firstname} {p.UpdatedBy.Lastname}"))
                .ForMember(dest => dest.VendorName, opt => opt.MapFrom(src => src.Vendor.VendorName))
                .ForMember(dest => dest.ClientName, opt => opt.MapFrom(src => src.VoyageId.HasValue ? src.Voyage.Client.Name : src.Client.Name))
                .ForMember(dest => dest.SiteName, opt => opt.MapFrom(src => src.VoyageId.HasValue ? src.Voyage.Site.Name : src.Site.Name))
                .ForMember(dest => dest.VoyageVesselName, opt => opt.MapFrom(src => src.Voyage.Vessel.Name))
                .ForMember(d => d.CargoLength, s => s.MapFrom(p => p.CargoLengthMm))
                .ForMember(d => d.CargoWeight, s => s.MapFrom(p => p.CargoWeightKg))
                .ForMember(d => d.CargoWidth, s => s.MapFrom(p => p.CargoWidthMm))
                .ForMember(dest => dest.MeasurementUnit, opt => opt.Ignore())
                .ForMember(d => d.PassedInspection, s => s.MapFrom(p => p.VoyageCargoInspection.Status == VoyageCargoInspectionStatus.Passed))
                .ForMember(d => d.VendorName, s => s.MapFrom(p => p.Vendor != null ? p.Vendor.VendorName : ""))
                .ReverseMap();


            CreateMap<VoyageCargo, VoyageCargoLiftingListModel>();

            CreateMap<VoyageCargo, VoyageCargoDeckPlanModel>();

            CreateMap<VoyageCargoDeckPlanUpdateModel, VoyageCargo>();

            CreateMap<VoyageCargoUpsertModel, VoyageCargo>()
                .ForMember(d => d.CargoLengthMm, s => s.MapFrom(p => p.CargoLength))
                .ForMember(d => d.CargoWeightKg, s => s.MapFrom(p => p.CargoWeight))
                .ForMember(d => d.CargoWidthMm, s => s.MapFrom(p => p.CargoWidth))
                .ForMember(d => d.VoyageCargoId, s => s.Ignore())
                .ForMember(d => d.VoyageCargoDangerousGoods, s => s.Ignore());

            CreateMap<VoyageCargoExtraCreateModel, VoyageCargo>();

            CreateMap<VoyageCargoInspection, VoyageCargoInspectionSnapshotModel>();

            CreateMap<VoyageCargo, VoyageCargo>()
                .ForMember(dest => dest.OriginalQuantity, opt => opt.Ignore())
                .ForMember(dest => dest.OriginalActualWeight, opt => opt.Ignore());

            CreateMap<VoyageCargoModel, VoyageCargo>()
                .ForMember(d => d.CargoLengthMm, s => s.MapFrom(p => p.CargoLength))
                .ForMember(d => d.CargoWeightKg, s => s.MapFrom(p => p.CargoWeight))
                .ForMember(d => d.CargoWidthMm, s => s.MapFrom(p => p.CargoWidth))
                   .ForMember(d => d.VoyageCargoDangerousGoods, s => s.Ignore());
        }
    }
}
