<div class="hire-request-list-table">
    <div class="d-flex justify-content-between align-items-center pb-20 gap-4">
        <h2>CCU Hires</h2>
    </div>

    <contain-hire-request-filters [isCargoHiresList]="true"></contain-hire-request-filters>

    <div *ngIf="selectedHireRequestCargoes?.length" class="d-flex justify-content-between align-items-center pb-20 gap-4">
        <p-button
                  (onClick)="openUpdateSelectedHiresDialog()"
                  label="Update Selected Hires"
                  type="button"
                  size="small"
                  [outlined]="true"
                  severity="secondary">
        </p-button>
    </div>

    <p-table 
        [columns]="listColumns()" 
        [value]="rows!" 
        [scrollable]="true"
        scrollHeight="calc(100vh - 273px)"
        [rowsPerPageOptions]="[20, 25, 50]"
        [paginator]="true"
        [rows]="20">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                    [style.width.%]="(column.width / tableWidth) * 100">
                    <span>{{ column.name }}</span>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr [ngStyle]="{'background-color': isRowSelected(item.hireRequestCargoId) ? '#b0bed9' : ''}">
                <td>
                    <p-checkbox
                                [id]="item.hireRequestCargoId"
                                (onChange)="setSelectedRow($event.checked, item.hireRequestCargoId)"
                                [binary]="true"
                                [inputId]="item.hireRequestCargoId">
                    </p-checkbox>
                </td>
                <td>{{item.cargoCCUId }}</td>
                <td>{{item.cargoDescription }}</td>
                <td>{{item.cargoUnitType }}</td>
                <td>{{item.onHiredDate | date:'dd/MM/yyyy HH:mm' }}</td>
                <td>{{item.clientName }}</td>
                <td>{{item.billingAssetName }}</td>
                <td>{{item.reference}}</td>
                <td>{{item.shipped | date: 'dd/MM/yyyy HH:mm'}}</td>
                <td>{{item.manifestOut}}</td>
                <td>{{item.assetName}}</td>
                <td>{{item.returned | date: 'dd/MM/yyyy HH:mm'}}</td>
                <td>{{item.manifestIn}}</td>
                <td>{{item.vendorInbound}}</td>
                <td>{{item.offHiredDate | date: 'dd/MM/yyyy HH:mm'}}</td>
                <td>
                    <i
                       title="View Hire Details"
                       class="pi pi-info-circle"
                       (click)="openDetailsDialog(item)">
                    </i>
                </td>
                <td>
                    <i
                      *ngIf="item.onHiredDate &&!item.offHiredDate && item.returned"
                       title="Set Off-Hired Date"
                       class="pi pi-flag-fill"
                       (click)="openSetOffHiredDateDialog(item)">
                    </i>
                </td>
            </tr>
        </ng-template>
    </p-table>

    <contain-cargo-hire-details-dialog
        *ngIf="detailsDialogVisible && selectedHireRequestCargo"
        [dialogVisible]="detailsDialogVisible"
        (dialogToggle)="setDialogVisible()"
        [hireRequestCargoInput]="selectedHireRequestCargo">
    </contain-cargo-hire-details-dialog>

    <contain-set-cargo-off-hire-date-dialog
    *ngIf="offHiredDialogVisible && selectedHireRequestCargo"
    [dialogVisible]="offHiredDialogVisible"
    (dialogToggle)="setOffHiredDateDialogVisible()"
    [hireRequestCargo]="selectedHireRequestCargo">        
    </contain-set-cargo-off-hire-date-dialog>

    <contain-cargo-hire-batch-update-dialog
    *ngIf="updateSelectedHiresDialogVisible && selectedHireRequestCargoes?.length"
    [dialogVisible]="updateSelectedHiresDialogVisible"
    [selectedHireRequestCargoes]="selectedHireRequestCargoes"
    (dialogToggle)="setUpdateSelectedHiresDialogVisible()">        
    </contain-cargo-hire-batch-update-dialog>
</div>