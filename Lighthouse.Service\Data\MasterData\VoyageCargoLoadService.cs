﻿namespace Lighthouse.Service.Data.MasterData {
    public class VoyageCargoLoadService : IVoyageCargoLoadService {

        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IVoyageCargoWeightUtility _voyageCargoWeightUtility;

        public VoyageCargoLoadService(IUnitOfWork unitOfWork, IMapper mapper, IUserService userService, IVoyageCargoWeightUtility voyageCargoWeightUtility) {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            _voyageCargoWeightUtility = voyageCargoWeightUtility;
        }
        public async Task<IList<VoyageCargoLoadModel>> GetVoyageCargoLoadsAsync() {
            var voyageCargoLoads = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Select(d => new VoyageCargoLoadModel {
                    VoyageCargoLoadId = d.VoyageCargoLoadId,
                    LoadType = d.LoadType,
                    LocationId = d.LocationId,
                    TrailerId = d.TrailerId,
                    TrailerRegistrationNumber = d.Trailer.RegistrationNumber,
                    VehicleId = d.VehicleId,
                    VehicleRegistrationNumber = d.Vehicle != null ? d.Vehicle.RegistrationNumber : null,
                    DriverId = d.DriverId,
                    DriverName = d.Driver != null ? d.Driver.FirstName + " " + d.Driver.LastName : null,
                    WideLoad = d.WideLoad,
                    Extender = d.Extender,
                    Comments = d.Comments,
                    CreatedDate = d.CreatedDate,
                    UpdatedDate = d.UpdatedDate,
                    CreatedByName = d.CreatedBy.Firstname + " " + d.CreatedBy.Lastname,
                    UpdatedByName = d.UpdatedBy != null ? d.UpdatedBy.Firstname + " " + d.UpdatedBy.Lastname : null,
                })
                .ToListAsync();

            return voyageCargoLoads;
        }

        public async Task<VoyageCargoLoadModel> GetVoyageCargoLoadByIdAsync(Guid voyageCargoLoadId) {
            var voyageCargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query(x => x.VoyageCargoLoadId == voyageCargoLoadId)
                .SingleOrDefaultAsync();

            return _mapper.Map<VoyageCargoLoadModel>(voyageCargoLoad);
        }

        public async Task<IList<VoyageCargoLoadModel>> GetVoyageCargoLoadsByLocationIdAsync(Guid locationId) {
            var voyageCargoLoads = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query(x => x.LocationId == locationId)
                .AsSplitQuery()
                .AsNoTracking()
                .Select(d => new VoyageCargoLoadModel {
                    VoyageCargoLoadId = d.VoyageCargoLoadId,
                    LoadType = d.LoadType,
                    LocationId = d.LocationId,
                    TrailerId = d.TrailerId,
                    TrailerRegistrationNumber = d.Trailer.RegistrationNumber,
                    VehicleId = d.VehicleId,
                    VehicleRegistrationNumber = d.Vehicle != null ? d.Vehicle.RegistrationNumber : null,
                    DriverId = d.DriverId,
                    DriverName = d.Driver != null ? d.Driver.FirstName + " " + d.Driver.LastName : null,
                    WideLoad = d.WideLoad,
                    Extender = d.Extender,
                    Comments = d.Comments,
                    CreatedDate = d.CreatedDate,
                    UpdatedDate = d.UpdatedDate,
                    CreatedByName = d.CreatedBy.Firstname + " " + d.CreatedBy.Lastname,
                    UpdatedByName = d.UpdatedBy != null ? d.UpdatedBy.Firstname + " " + d.UpdatedBy.Lastname : null,
                    VoyageCargos = _mapper.Map<List<VoyageCargoModel>>(d.VoyageCargos)
                })
                .ToListAsync();

            return voyageCargoLoads;
        }

        public async Task<VoyageCargoLoadModel> CreateVoyageCargoLoadAsync(VoyageCargoLoadUpsertModel model, ClaimsPrincipal user) {
            var createdBy = (await _userService.GetCurrentUser(user));
            await _unitOfWork.BeginTransactionAsync();
            if (model.OpenLoadSelection == VoyageCargoOpenLoadSelection.ExistingLoad && model.LoadType == VoyageCargoLoadType.TrailerDrop) {
                await AddToExistingLoad(model, createdBy);
                await _unitOfWork.CommitAsync();
                return new VoyageCargoLoadModel();
            } else if (model.OpenLoadSelection == VoyageCargoOpenLoadSelection.NewLoad && model.LoadType == VoyageCargoLoadType.TrailerDrop) {
                var openVoyageCargoLoad = await GetUnDispatchedOpenVoyageCargoLoad(model.LocationId, model.TrailerRegistrationNumber);
                await DispatchOpenVoyageCargoLoad(openVoyageCargoLoad, createdBy.UserId);
                return await CreateLoad(model, createdBy);
            } else {
                return await CreateLoad(model, createdBy);
            }
        }

        public async Task<VoyageCargoLoadModel> CreateLoad(VoyageCargoLoadUpsertModel model, UserModel user) {
            var trailerId = await _unitOfWork.Repository<Trailer>()
                .Query(x => x.RegistrationNumber == model.TrailerRegistrationNumber)
                .Select(x => x.TrailerId)
                .FirstOrDefaultAsync();

            if (trailerId == Guid.Empty) {
                var trailer = new Trailer {
                    RegistrationNumber = model.TrailerRegistrationNumber,
                    LocationId = model.LocationId,
                    CreatedById = user.UserId,
                    CreatedDate = DateTime.UtcNow,
                    IsAdhoc = true,
                };
                trailer = await _unitOfWork.Repository<Trailer>().CreateAsync(trailer);
                trailerId = trailer.TrailerId;
            }

            Guid? vehicleId = null;
            if (!string.IsNullOrEmpty(model.VehicleRegistrationNumber)) {
                vehicleId = await _unitOfWork.Repository<Vehicle>()
                    .Query(x => x.RegistrationNumber == model.VehicleRegistrationNumber)
                    .Select(x => x.VehicleId)
                    .FirstOrDefaultAsync();

                if (vehicleId == Guid.Empty) {
                    var vehicle = new Vehicle {
                        RegistrationNumber = model.VehicleRegistrationNumber,
                        LocationId = model.LocationId,
                        CreatedById = user.UserId,
                        CreatedDate = DateTime.UtcNow,
                        IsAdhoc = true,
                    };
                    vehicle = await _unitOfWork.Repository<Vehicle>().CreateAsync(vehicle);
                    vehicleId = vehicle.VehicleId;
                }
            }

            Guid? driverId = null;
            if (!string.IsNullOrEmpty(model.DriverName)) {
                var names = model.DriverName.Split(' ');
                var firstName = names[0];
                var lastName = names.Length > 1 ? string.Join(' ', names.Skip(1)) : string.Empty;

                driverId = await _unitOfWork.Repository<Driver>()
                    .Query(x => x.FirstName == firstName && x.LastName == lastName)
                    .Select(x => x.DriverId)
                    .FirstOrDefaultAsync();

                if (driverId == Guid.Empty) {
                    var driver = new Driver {
                        FirstName = firstName,
                        LastName = lastName,
                        LocationId = model.LocationId,
                        CreatedById = user.UserId,
                        CreatedDate = DateTime.UtcNow,
                        IsAdhoc = true,
                    };
                    driver = await _unitOfWork.Repository<Driver>().CreateAsync(driver);
                    driverId = driver.DriverId;
                }
            }

            var voyageCargoLoad = new VoyageCargoLoad {
                LoadType = model.LoadType,
                LocationId = model.LocationId,
                TrailerId = trailerId,
                VehicleId = vehicleId,
                DriverId = driverId,
                WideLoad = model.WideLoad,
                Extender = model.Extender,
                Comments = model.Comments,
                CreatedById = user.UserId,
                CreatedDate = DateTime.UtcNow,
            };

            voyageCargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>().CreateAsync(voyageCargoLoad);

            await CreateOrSplitCargoLoad(voyageCargoLoad, model, user);

            await _unitOfWork.CommitAsync();

            return _mapper.Map<VoyageCargoLoadModel>(voyageCargoLoad);
        }

        public async Task AddToExistingLoad(VoyageCargoLoadUpsertModel model, UserModel user) {
            var voyageCargoLoad = await GetUnDispatchedOpenVoyageCargoLoad(model.LocationId, model.TrailerRegistrationNumber);

            await CreateOrSplitCargoLoad(voyageCargoLoad, model, user);
        }

        public async Task CreateOrSplitCargoLoad(VoyageCargoLoad voyageCargoLoad, VoyageCargoLoadUpsertModel model, UserModel user) {
            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>().Query()
                .Where(x => model.VoyageCargos.Select(vc => vc.VoyageCargoId).Contains(x.VoyageCargoId))
                .ToListAsync();

            var measurementUnit = Enum.Parse<LocationMeasurementUnit>(user.MeasurementUnit, ignoreCase: true);

            foreach (var voyageCargo in voyageCargos) {
                var modelCargo = model.VoyageCargos.FirstOrDefault(x => x.VoyageCargoId == voyageCargo.VoyageCargoId);

                var convertedActualWeight = _voyageCargoWeightUtility.ConvertWeight(modelCargo.ActualWeight, measurementUnit, false);

                if (modelCargo.Quantity != voyageCargo.Quantity && modelCargo.ActualWeight != voyageCargo.ActualWeight) {
                    var splitCargo = _mapper.Map<VoyageCargo>(voyageCargo);
                    splitCargo.VoyageCargoId = Guid.NewGuid();
                    splitCargo.Quantity = modelCargo.Quantity;
                    splitCargo.ActualWeight = convertedActualWeight;
                    splitCargo.VoyageCargoLoadId = voyageCargoLoad.VoyageCargoLoadId;
                    splitCargo.VoyageCargoParentId = voyageCargo.VoyageCargoId;
                    splitCargo.TrailerId = voyageCargoLoad.TrailerId;
                    splitCargo.TrailerNumber = model.TrailerRegistrationNumber;
                    splitCargo.CargoBackloadStatus = CargoBackloadStatus.OnTrailer;
                    splitCargo.TransportStatus = voyageCargo.TransportStatus;
                    splitCargo.CreatedById = user.UserId;
                    splitCargo.CreatedDate = DateTime.UtcNow;
                    splitCargo.UpdatedById = user.UserId;
                    splitCargo.UpdatedDate = DateTime.UtcNow;

                    splitCargo = await _unitOfWork.Repository<VoyageCargo>().CreateAsync(splitCargo);

                    splitCargo.CargoWeightKg = null;

                    await _unitOfWork.SaveChangesAsync();

                    if (voyageCargo.OriginalQuantity == null && voyageCargo.OriginalActualWeight == null) {
                        voyageCargo.OriginalQuantity = voyageCargo.Quantity;
                        voyageCargo.OriginalActualWeight = voyageCargo.ActualWeight;
                    }
                    voyageCargo.Quantity = voyageCargo.Quantity - modelCargo.Quantity;
                    voyageCargo.ActualWeight = voyageCargo.ActualWeight - convertedActualWeight;
                    if (voyageCargo.TrailerNumber != null || voyageCargo.TrailerId != null) {
                        voyageCargo.TrailerNumber = null;
                        voyageCargo.TrailerId = null;
                    }

                    _unitOfWork.Repository<VoyageCargo>().Update(voyageCargo);
                    await _unitOfWork.SaveChangesAsync();
                } else {
                    voyageCargo.VoyageCargoLoadId = voyageCargoLoad.VoyageCargoLoadId;
                    voyageCargo.TrailerId = voyageCargoLoad.TrailerId;
                    voyageCargo.TrailerNumber = model.TrailerRegistrationNumber;
                    voyageCargo.CargoBackloadStatus = CargoBackloadStatus.OnTrailer;
                    voyageCargo.UpdatedDate = DateTime.UtcNow;
                    voyageCargo.UpdatedById = user.UserId;
                }
            }

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task DispatchOpenVoyageCargoLoad(VoyageCargoLoad voyageCargoLoad, Guid userId) {
            voyageCargoLoad.DispatchDate = DateTime.UtcNow;
            voyageCargoLoad.LoadStatus = VoyageCargoLoadStatus.Dispatched;
            voyageCargoLoad.PaperworkCompleteDate = DateTime.UtcNow;

            await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoLoad.VoyageCargos.Select(x => x.VoyageCargoId).Contains(x.VoyageCargoId))
                .ExecuteUpdateAsync(x => x
                .SetProperty(x => x.DispatchTime, DateTime.UtcNow)
                .SetProperty(x => x.CargoBackloadStatus, CargoBackloadStatus.Dispatched)
                .SetProperty(x => x.UpdatedDate, DateTime.UtcNow)
                .SetProperty(x => x.UpdatedById, userId));

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<VoyageCargoLoadModel> UpdateVoyageCargoLoadAsync(Guid id, VoyageCargoLoadUpsertModel model,
            ClaimsPrincipal user) {
            var updatedBy = _userService.GetCurrentUser(user).Result.UserId;
            var voyageCargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query()
                .Where(x => x.VoyageCargoLoadId == id)
                .SingleOrDefaultAsync();
            
            if (model.LoadStatus == VoyageCargoLoadStatus.Dispatched)
            {
                await _unitOfWork.Repository<VoyageCargo>()
                    .Query(x => x.VoyageCargoLoadId == id)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.CargoBackloadStatus, CargoBackloadStatus.Dispatched)
                        .SetProperty(p => p.DispatchTime, model.DispatchDate));
            }

            if (voyageCargoLoad.LoadStatus == VoyageCargoLoadStatus.Dispatched &&
                model.LoadStatus == VoyageCargoLoadStatus.PaperworkComplete)
            {
                var trailerList = await _unitOfWork.Repository<VoyageCargoLoad>()
                    .Query(x => x.TrailerId == voyageCargoLoad.TrailerId).ToListAsync();

                if (trailerList.Any(x => x.LoadStatus == VoyageCargoLoadStatus.PaperworkComplete | x.LoadStatus == VoyageCargoLoadStatus.PaperworkNotComplete))
                {
                    throw new Exception(
                        "Undo dispatch can not be done, as there is open load with the same trailer number");
                }
                
                await _unitOfWork.Repository<VoyageCargo>()
                    .Query(x => x.VoyageCargoLoadId == id)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.CargoBackloadStatus, CargoBackloadStatus.OnTrailer)
                        .SetProperty(p => p.DispatchTime, model.DispatchDate));
            }

            _mapper.Map(model, voyageCargoLoad);

            voyageCargoLoad.UpdatedDate = DateTime.UtcNow;
            voyageCargoLoad.UpdatedById = updatedBy;

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<VoyageCargoLoadModel>(voyageCargoLoad);
        }

        public async Task<List<VoyageCargoLoadModel>> VoyageCargoLoadsFiltersAsync(
            VoyageCargoLoadFilterModel filterModel, UserModel user) {
            var baseQuery = _unitOfWork.Repository<VoyageCargoLoad>()
                .Query();

            var query = baseQuery
                .AsNoTracking()
                .AsSplitQuery()
                .Where(x => x.LocationId == filterModel.LocationId);
            

            if (!string.IsNullOrEmpty(filterModel.Search)) {
                query = query.Where(x =>
                    x.VoyageCargos.Any(vc => vc.CcuId.ToLower().Contains(filterModel.Search.ToLower())));
            }

            if (filterModel.From.HasValue) {
                query = query.Where(x => x.CreatedDate >= filterModel.From.Value);
            }

            if (filterModel.To.HasValue) {
                query = query.Where(x => x.CreatedDate <= filterModel.To.Value);
            }

            if (filterModel.LoadStatus?.Any() == true) {
                query = query.Where(x => x.LoadStatus.HasValue &&
                                         filterModel.LoadStatus.Contains(x.LoadStatus.Value));
            }

            if (filterModel.LoadTypes?.Any() == true) {
                query = query.Where(x => filterModel.LoadTypes.Contains(x.LoadType));
            }

            if (filterModel.DriverIds?.Any() == true) {
                query = query.Where(x => x.DriverId.HasValue &&
                                         filterModel.DriverIds.Contains(x.DriverId.Value));
            }

            if (filterModel.VehicleIds?.Any() == true) {
                query = query.Where(x => x.VehicleId.HasValue &&
                                         filterModel.VehicleIds.Contains(x.VehicleId.Value));
            }

            if (filterModel.TrailerIds?.Any() == true) {
                query = query.Where(x => filterModel.TrailerIds.Contains(x.TrailerId));
            }

            if (filterModel.SiteIds?.Any() == true) {
                query = query.Where(x => x.VoyageCargos.Any(vc =>
                    (vc.VoyageId.HasValue && filterModel.SiteIds.Contains(vc.Voyage.SiteId)) ||
                    (!vc.VoyageId.HasValue && filterModel.SiteIds.Contains(vc.SiteId))));
            }

            var voyageCargoLoads = await query.Select(x => new VoyageCargoLoadModel {
                VoyageCargoLoadId = x.VoyageCargoLoadId,
                LoadType = x.LoadType,
                LoadStatus = x.LoadStatus,
                LocationId = x.LocationId,
                TrailerId = x.TrailerId,
                TrailerRegistrationNumber = x.Trailer.RegistrationNumber,
                VehicleId = x.VehicleId,
                VehicleRegistrationNumber = x.Vehicle.RegistrationNumber,
                DriverId = x.DriverId,
                DriverName = x.Driver.FirstName + ' ' + x.Driver.LastName,
                WideLoad = x.WideLoad,
                Extender = x.Extender,
                Comments = x.Comments,
                CreatedDate = x.CreatedDate,
                UpdatedDate = x.UpdatedDate,
                Deleted = x.Deleted,
                PaperworkCompleteDate = x.PaperworkCompleteDate,
                DispatchDate = x.DispatchDate,
                VoyageCargos = x.VoyageCargos.Select(x => new VoyageCargoModel {
                    VoyageCargoId = x.VoyageCargoId,
                    VoyageId = x.VoyageId,
                    CcuId = x.CcuId,
                    VoyageCargoLoadId = x.VoyageCargoLoadId,
                    Quantity = x.Quantity,
                    ActualWeight = x.ActualWeight,
                    IsHighPriority = x.IsHighPriority,
                    CargoBackloadStatus = x.CargoBackloadStatus,
                    TransportRequestCargoId = x.TransportRequestCargoId,
                    CargoId = x.CargoId,
                    CargoDescription = x.CargoDescription,
                    CargoLength = x.CargoLengthMm ?? 0,
                    CargoUnitType = x.CargoUnitType,
                    CargoWeight = x.CargoWeightKg ?? 0,
                    CargoWidth = x.CargoWidthMm ?? 0,
                    Comments = x.Comments,
                    DeckLocation = x.DeckLocation,
                    Level = x.Level,
                    NumberOfLifts = x.NumberOfLifts,
                    PriorityOrder = x.PriorityOrder,
                    Status = x.Status,
                    TransportAddress = x.TransportAddress,
                    TransportName = x.TransportName,
                    TransportPhoneNumber = x.TransportPhoneNumber,
                    TransportRequest = x.TransportRequest,
                    CustomReferenceNo = x.CustomReferenceNo,
                    VendorId = x.VendorId,
                    Owner = x.Owner,
                    AssetId = x.AssetId,
                    ViaVendorId = x.ViaVendorId,
                    TransportStatus = x.TransportStatus,
                    VendorWarehouseId = x.VendorWarehouseId,
                    WeightCategoryId = x.WeightCategoryId,
                    ViaVendorWarehouseId = x.ViaVendorWarehouseId,
                    DistrictId = x.DistrictId,
                    SiteId = x.SiteId,
                    CustomStatus = x.CustomStatus,
                    CustomsEntryType = x.CustomsEntryType,
                    ClientName = x.VoyageId != null ? x.Voyage.Client.Name : x.Client.Name,
                    VoyageVesselName = x.VoyageId != null ? x.Voyage.Vessel.Name : null,
                    VendorName = x.VendorId != null ? x.Vendor.VendorName : null,
                    SiteName = x.VoyageId != null ? x.Voyage.Site.Name : x.Site.Name,
                    CargoBackloadDischargeDate = x.CargoBackloadDischargeDate != null ? x.CargoBackloadDischargeDate : x.VoyageId != null ? x.Voyage.SailingDischargeDate : null
                }).ToList()
            }).ToListAsync();

            voyageCargoLoads = string.Equals(filterModel.OrderBy, "ASC", StringComparison.OrdinalIgnoreCase)
                ? voyageCargoLoads.OrderBy(x => x.CreatedDate).ToList()
                : voyageCargoLoads.OrderByDescending(x => x.CreatedDate).ToList();

            return voyageCargoLoads;
        }

        public async Task<List<VoyageCargoModel>> GetVoyageCargoesForOpenLoadAsync(Guid locationId, string trailerNumber, Guid siteId) {
            var trailerId = await _unitOfWork.Repository<Trailer>()
                .Query(x => x.RegistrationNumber == trailerNumber)
                .Select(x => x.TrailerId)
                .FirstOrDefaultAsync();

            if(trailerId != Guid.Empty) {
                var voyageCargoes = await _unitOfWork.Repository<VoyageCargoLoad>()
                    .Query(x => x.TrailerId == trailerId && x.LocationId == locationId && x.LoadStatus != VoyageCargoLoadStatus.Dispatched)
                    .SelectMany(x => x.VoyageCargos.Where(x => x.SiteId == siteId))
                    .Select(vc => new VoyageCargoModel {
                        VoyageCargoId = vc.VoyageCargoId,
                        CcuId = vc.CcuId,
                        CargoDescription = vc.CargoDescription,
                        VendorName = vc.Vendor.VendorName
                    })
                    .ToListAsync();

                if(voyageCargoes.Count > 0) {
                    return voyageCargoes;
                }
            }

            return new List<VoyageCargoModel>();
        }

        public async Task<VoyageCargoLoad> GetUnDispatchedOpenVoyageCargoLoad(Guid locationId, string trailerNumber) {
            var trailerId = await _unitOfWork.Repository<Trailer>()
                .Query(x => x.RegistrationNumber == trailerNumber)
                .Select(x => x.TrailerId)
                .FirstOrDefaultAsync();

            var voyageCargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query(x => x.TrailerId == trailerId && x.LocationId == locationId && x.LoadStatus != VoyageCargoLoadStatus.Dispatched)
                .Include(x => x.VoyageCargos)
                .FirstOrDefaultAsync();

            return voyageCargoLoad;
        }

        public async Task<List<(byte[] Content, string FileName)>> GenerateConsignmentNote(Guid voyageCargoLoadId, UserModel user) {
            var measurementUnit = Enum.Parse<LocationMeasurementUnit>(user.MeasurementUnit, ignoreCase: true);

            var voyageCargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>()
                .Query(x => x.VoyageCargoLoadId == voyageCargoLoadId)
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.VoyageCargos)
                    .ThenInclude(x => x.VoyageCargoDangerousGoods)
                        .ThenInclude(x => x.DangerousGood)
                .Include(x => x.VoyageCargos)
                    .ThenInclude(x => x.Asset)
                .Select(x => new VoyageCargoLoadModel {
                    VoyageCargoLoadId = x.VoyageCargoLoadId,
                    LocationId = x.LocationId,
                    LocationWasteCarrierCode = x.Location.WasteCarrierCode,
                    DispatchDate = x.DispatchDate,
                    VoyageCargos = x.VoyageCargos.Select(vc => new VoyageCargoModel {
                        VoyageCargoId = vc.VoyageCargoId,
                        VoyageId = vc.VoyageId,
                        SiteName = vc.VoyageId != null ? vc.Voyage.Site.Name : vc.Site.Name,
                        ClientName = vc.VoyageId != null ? vc.Voyage.Client.Name : vc.Client.Name,
                        ClientEuNumber = vc.Voyage != null ? vc.Voyage.Client.EUNumber : vc.Client.EUNumber,
                        VendorName = vc.Vendor != null ? vc.Vendor.VendorName : null,
                        VendorWarehouseName = vc.VendorWarehouse != null ? vc.VendorWarehouse.Name : null,
                        VendorWarehouseAddress = vc.VendorWarehouse != null ? vc.VendorWarehouse.Address : null,
                        VendorWarehouseCity = vc.VendorWarehouse != null ? vc.VendorWarehouse.City : null,
                        VendorWarehousePostCode = vc.VendorWarehouse != null ? vc.VendorWarehouse.PostCode : null,
                        VendorWarehouseDistrictName = vc.VendorWarehouse != null ? vc.VendorWarehouse.District.DistrictName : null,
                        Quantity = vc.Quantity,
                        CcuId = vc.CcuId,
                        CargoDescription = vc.CargoDescription,
                        CustomStatus = vc.CustomStatus,
                        CargoBackloadDischargeDate = vc.CargoBackloadDischargeDate != null ? vc.CargoBackloadDischargeDate: vc.VoyageId != null ? vc.Voyage.SailingDischargeDate: null,
                        VoyageCargoDangerousGoods = _mapper.Map<List<VoyageCargoDangerousGoodModel>>(vc.VoyageCargoDangerousGoods),
                        VoyageVesselName = vc.VoyageId != null ? vc.Voyage.Vessel.Name : null,
                        ActualWeight = _voyageCargoWeightUtility.ConvertWeight(vc.ActualWeight, measurementUnit, true),
                        ManifestNumbers = vc.VoyageId != null ? vc.Asset.VoyageOffshoreLocations.Where(x => x.VoyageId == vc.VoyageId).Select(x => x.ManifestNumber).ToList() : new List<string>(),
                        LocationName = vc.Asset != null ? vc.Asset.Name : null,
                    }).ToList()
                })
                .SingleOrDefaultAsync();

            if (voyageCargoLoad == null) {
                throw new Exception("Voyage cargo load not found");
            }

            var result = new List<(byte[] Content, string FileName)>();

            var cargoGroups = voyageCargoLoad.VoyageCargos
                .GroupBy(vc => new { vc.ClientName, vc.VendorName });

            foreach (var group in cargoGroups) {
                var groupedVoyageCargoLoad = new VoyageCargoLoadModel {
                    VoyageCargoLoadId = voyageCargoLoad.VoyageCargoLoadId,
                    LocationId = voyageCargoLoad.LocationId,
                    LocationName = voyageCargoLoad.LocationName,
                    LocationWasteCarrierCode = voyageCargoLoad.LocationWasteCarrierCode,
                    DispatchDate = voyageCargoLoad.DispatchDate,
                    VoyageCargos = group.ToList()
                };

                var pdfContent = VoyageCargoLoadConsignmentNoteGenerator.Generate(groupedVoyageCargoLoad, user);

                var fileName = $"ConsignmentNote_{group.Key.ClientName}_{group.Key.VendorName}.pdf";

                result.Add((pdfContent, fileName));
            }

            return result;
        }

    }
}
