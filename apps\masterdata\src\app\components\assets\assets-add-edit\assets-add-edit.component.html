<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ asset() ? 'Edit' : 'Add' }} Asset
      <em
        *ngIf="asset()"
        class="pi pi-info-circle"
        [pTooltip]="tooltipContent"
      ></em>
      <ng-template #tooltipContent>
        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="asset()?.createdByName"
        >
          <span>Created By: {{ asset()?.createdByName }}</span>
          <span
            >Created Date:
            {{ asset()?.createdDate | date : 'dd/MM/yyyy HH:mm' }}</span
          >
        </div>

        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="asset()?.updatedByName"
        >
          <span>Last Updated By: {{ asset()?.updatedByName }}</span>
          <span
            >Updated Date:
            {{ asset()?.updatedDate | date : 'dd/MM/yyyy HH:mm' }}</span
          >
        </div>
      </ng-template>
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Asset Name</label>
          <input pInputText type="text" formControlName="name" />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Asset Name is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Asset Type</label>
          <p-dropdown
            [options]="assetTypesList()"
            [filter]="true"
            formControlName="assetType"
            optionLabel="description"
            optionValue="value"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.assetType?.invalid && controls.assetType?.touched"
          >
            Asset type is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 flex-direction-column gap-4">
        <label class="fs-14">Locations</label>
        <p-multiSelect
          #multiSelectClientLocations
          inputId="float-voyage-sharer"
          [options]="locations()"
          formControlName="locationIds"
          optionLabel="name"
          optionValue="locationId"
          [showToggleAll]="!multiSelectClientLocations.isEmpty()"
          display="comma"
          [showClear]="!!controls.locationIds?.value?.length"
          appendTo="body"
          styleClass="new-version-multiselect"
          panelStyleClass="new-version-panel"
        ></p-multiSelect>
        <small
          class="validation-control-error"
          *ngIf="controls.locationIds?.invalid && controls.locationIds?.touched"
        >
          Locations is required.
        </small>
      </div>
      <ng-container
        *ngIf="
          locDependencies.locTypeOff
            | arrayIncludes : form.controls.assetType.value
        "
      >
        <div class="w-100 f-bold mt-20 mb-50">
          Assign Operator & Cluster Head
        </div>
        <p-divider class="w-100 mb-10" />
        <div *ngIf="asset()" class="d-flex w-100 justify-content-between gap-8">
          <div class="d-flex w-50 align-items-center gap-4">
            <p-inputSwitch
              formControlName="addNewHistory"
              (onChange)="onAddNewHistoryChange($event.checked)"
            />
            <label class="fs-14">Add New History</label>
          </div>
        </div>
        <div class="d-flex w-100 justify-content-between gap-8">
          <div class="d-flex w-50 flex-direction-column gap-4">
            <label for="startDateTime" class="fs-14">Operator</label>
            <p-dropdown
              [options]="operators()"
              [filter]="true"
              formControlName="clientId"
              optionLabel="name"
              optionValue="clientId"
              [showClear]="true"
              appendTo="body"
              panelStyleClass="new-version-panel"
              styleClass="new-version"
            />
            <small
              class="validation-control-error"
              *ngIf="controls.clientId?.invalid && controls.clientId?.touched"
            >
              Operator is required.
            </small>
          </div>
          <div class="d-flex w-50 flex-direction-column gap-4">
            <label for="endDateTime" class="fs-14"
              >Cluster Head (optional)</label
            >
            <p-dropdown
              [options]="assetsHeads()"
              [filter]="true"
              [showClear]="true"
              formControlName="clusterHeadId"
              optionLabel="name"
              optionValue="assetId"
              appendTo="body"
              panelStyleClass="new-version-panel"
              styleClass="new-version"
            />
            <small
              class="validation-control-error"
              *ngIf="asset()?.isClusterHead && controls.clusterHeadId?.disabled"
            >
              Asset is a cluster head.
            </small>
          </div>
        </div>
        <div
          class="d-flex w-100 justify-content-between gap-8"
          *ngIf="
            locDependencies.locTypeOff
              | arrayIncludes : form.controls.assetType.value
          "
        >
          <div class="d-flex w-50 flex-direction-column gap-4">
            <label for="startDateTime" class="fs-14">Start Date Time</label>
            <p-calendar
              [showTime]="true"
              [showSeconds]="false"
              appendTo="body"
              formControlName="startDateTime"
              dateFormat="dd/mm/yy"
              (onClear)="controls.startDateTime?.setValue(null)"
              [maxDate]="controls.endDateTime?.value!"
            ></p-calendar>
            <small
              class="validation-control-error"
              *ngIf="
                controls.startDateTime?.invalid &&
                controls.startDateTime?.touched
              "
            >
              Start Date Time is required.
            </small>
          </div>
          <div class="d-flex w-50 flex-direction-column gap-4">
            <label for="endDateTime" class="fs-14">End Date Time</label>
            <p-calendar
              [showTime]="true"
              [showSeconds]="false"
              appendTo="body"
              formControlName="endDateTime"
              dateFormat="dd/mm/yy"
              (onClear)="controls.endDateTime?.setValue(null)"
              [minDate]="controls.startDateTime?.value!"
            ></p-calendar>
          </div>
        </div>
      </ng-container>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ asset() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
