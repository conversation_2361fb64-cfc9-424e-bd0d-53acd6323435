<div class="d-flex justify-content-between flex-wrap gap-16">
  <div class="flex-1" [style.minWidth.px]="400">
    <div class="d-flex justify-content-between mb-40">
      <b class="fs-24 flex-1">Hire Statement</b>

      <div class="d-flex gap-8 flex-1 flex-wrap justify-content-end">
        <span class="p-input-icon-left">
          <em class="pi pi-search"></em>
          <input
            type="text"
            pInputText
            [(ngModel)]="searchValue"
            placeholder="Search..."
            (input)="table.filterGlobal(searchValue, 'contains')"
          />
        </span>

        <button type="button" class="btn-primary" (click)="add()">
          Create
        </button>
        <button
          type="button"
          class="btn-export align-items-center d-flex"
          (click)="exportHireStatements()"
        >
          <img src="assets/icons/exel.svg" />
          Export
        </button>
      </div>
    </div>

    <p-table
      #table
      [columns]="listColumns"
      [value]="hireStatements"
      [scrollable]="true"
      scrollHeight="400px"
      [rowsPerPageOptions]="[10, 25, 50]"
      [paginator]="true"
      [rows]="10"
      [filterDelay]="0"
      [loading]="loading().list"
      [globalFilterFields]="[
        tableFields.type,
        tableFields.isOnHire,
        tableFields.deliveryDate,
        tableFields.deliveryPlace,
        tableFields.redeliveryDate,
        tableFields.dayRate,
        tableFields.redeliveryPlace,
        tableFields.duration
      ]"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let column of columns">
            <ng-container [ngSwitch]="column.field">
              <th
                *ngSwitchCase="tableFields.dayRate"
                scope="col"
                [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth) * 100"
              >
                <span>Day Rate{{ ' ' + appSettings()?.currency }}</span>
              </th>
              <th
                *ngSwitchDefault
                [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth) * 100"
                [pSortableColumn]="column.field"
                [pSortableColumnDisabled]="!column.sortable"
              >
                <span>{{ column.name }}</span>
                <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
              </th>
            </ng-container>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template
        let-index="rowIndex"
        pTemplate="body"
        let-row
        let-columns="columns"
      >
        <tr
          [routerLink]="[row.hireStatementId]"
          routerLinkActive="active"
          #itemLink="routerLinkActive"
          (click)="$event.stopPropagation()"
        >
          <ng-container *ngFor="let column of columns">
            <ng-container [ngSwitch]="column.field">
              <td *ngSwitchCase="tableFields.tableCheckbox">
                <p-radioButton
                  [ngModel]="itemLink.isActive"
                  [value]="true"
                ></p-radioButton>
              </td>
              <td *ngSwitchCase="tableFields.redeliveryDate">
                {{ row.redeliveryDate | date : 'dd/MM/yyyy HH:mm' }}
              </td>
              <td *ngSwitchCase="tableFields.deliveryDate">
                {{ row.deliveryDate | date : 'dd/MM/yyyy HH:mm' }}
              </td>
              <td *ngSwitchCase="tableFields.dayRate">
                {{ row.dayRate | number : '1.2-2' }}
              </td>

              <td *ngSwitchCase="tableFields.isOnHire">
                <span class="d-flex align-items-center justify-content-center">
                  <i
                    class="pi fs-16"
                    [ngClass]="row.isOnHire ? 'pi-check' : 'pi-times'"
                    [style.color]="row.isOnHire ? 'green' : 'red'"
                  >
                  </i>
                </span>
              </td>

              <td *ngSwitchCase="tableFields.actions">
                <div class="d-flex gap-8 flex-wrap">
                  <button
                    type="button"
                    class="btn-icon-only"
                    (click)="edit(row)"
                  >
                    <em class="pi pi-pencil"></em>
                  </button>

                  <button
                    type="button"
                    class="btn-icon-only"
                    (click)="remove(row)"
                  >
                    <em class="pi pi-trash"></em>
                  </button>
                </div>
              </td>

              <td *ngSwitchDefault>{{ row[column.field] }}</td>
            </ng-container>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" style="text-align: center">No results found</td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <lha-hire-statement-add-edit/>

  <div class="flex-1" [style.minWidth.px]="400">
    <router-outlet />
  </div>
</div>
