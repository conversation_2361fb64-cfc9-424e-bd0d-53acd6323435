import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { DialogModule } from 'primeng/dialog';
import {
  FormControl,
  FormGroup,
  FormsModule,
  Validators,
} from '@angular/forms';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { ReactiveFormsModule } from '@angular/forms';
import { HireRequestCargoSetOffHiredDate } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo-set-off-hire-date.interface';
import { DropdownModule } from 'primeng/dropdown';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { DividerModule } from 'primeng/divider';
import { DatePipe } from '@angular/common';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'contain-set-cargo-off-hire-date-dialog',
  standalone: true,
  imports: [
    LoadingDirective,
    NgForOf,
    NgIf,
    DialogModule,
    TableModule,
    ReactiveFormsModule,
    FormsModule,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    InputTextModule,
    InputTextareaModule,
    DividerModule,
    DatePipe,
  ],
  templateUrl: './cargo-hire-set-off-hire-date.dialog.html',
  styleUrls: ['./cargo-hire-set-off-hire-date.dialog.scss'],
})
export class CargoHireSetOffHireDateDialog implements OnInit {
  @Output() dialogToggle = new EventEmitter<void>();
  @Input() dialogVisible: boolean = false;
  @Input() hireRequestCargo!: HireRequestCargo;
  store = inject(Store);

  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);

  cargoHireForm: FormGroup = new FormGroup({});

  ngOnInit(): void {
    this.cargoHireForm = this.initialiseForm();
  }

  initialiseForm(): FormGroup {
    return new FormGroup({
      offHiredDate: new FormControl<Date | null>(null, Validators.required),
    });
  }

  submit(): void {
    this.cargoHireForm.markAllAsTouched();

    if (this.cargoHireForm.valid) {
      const model: HireRequestCargoSetOffHiredDate = {
        offHiredDate: stripTimezoneOffset(
          this.cargoHireForm.value.offHiredDate!
        ),
      };

      this.store.dispatch(
        HireRequestActions.set_Cargo_Hire_Off_Hired_Date({
          hireRequestCargoId: this.hireRequestCargo.hireRequestCargoId,
          hireRequestCargoSetOffHireDateModel: model,
          filterModel: this.filterModel(),
        })
      );

      this.hideDialog();

      this.cargoHireForm = this.initialiseForm();
    }
  }

  hideDialog(): void {
    this.dialogToggle.emit();
  }

  minOffHiredDate = computed(() => {
    const returnedDate = this.hireRequestCargo?.returned;

    if (returnedDate) {
      return new Date(returnedDate);
    }

    return new Date();
  });

  hasValidDatesForOffHiring = computed(() => {
    const returnedDate = this.hireRequestCargo?.returned;
    const onHiredDate = this.hireRequestCargo?.onHiredDate;

    if (returnedDate && onHiredDate) {
      return true;
    }

    return false;
  });
}
