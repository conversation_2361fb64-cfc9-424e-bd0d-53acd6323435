import { FieldType } from 'libs/components/src/lib/enums';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BulkTableFields } from '../../enums';

export function InitializeBulkTableViewMode(): ColumnModel[] {
  const bulkTableViewModeColumns = [
    new ColumnModel(BulkTableFields.rowNumber, 'No', 60),
    new ColumnModel(BulkTableFields.status, 'Status', 60, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.assetId, 'Asset', 200, {
      fieldType: FieldType.dropdown,
      validator: true,
    }),
    new ColumnModel(BulkTableFields.unitName, 'Unit', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.ppgsg, 'PPG/SG ', 100, {
      fieldType: FieldType.number,
      decimals: 2,
    }),
    new ColumnModel(BulkTableFields.siteId, 'Site', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.bulkTypeId, 'Type Of Bulk ', 100, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.quantity, 'Quantity ', 100, {
      fieldType: FieldType.number,
    }),
    new ColumnModel(BulkTableFields.vendorId, 'Vendor', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.voyageCargoBulkDangerousGoodId, 'Dangerous Good', 50, {
      fieldType: FieldType.readonlyIcon,
    }),
    new ColumnModel(BulkTableFields.unNo, 'UnNo', 200, {}),

    new ColumnModel(BulkTableFields.rtRob, 'RT/ROB', 200, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.weightCategoryId, 'Weight Cat.', 150, {
      fieldType: FieldType.dropdown,
    }),
    new ColumnModel(BulkTableFields.customStatus, 'Customs Status', 150, {
      fieldType: FieldType.dropdown,
    }),
  ];
  return bulkTableViewModeColumns;
}
