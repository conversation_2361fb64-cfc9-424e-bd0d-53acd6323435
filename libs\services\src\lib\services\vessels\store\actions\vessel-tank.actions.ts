import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { VesselTank } from '../../interfaces/vessel-tank.interface';
import { TankType } from '../../../maintenance/interfaces/tank-type.interface';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';
import { VesselTankRequest } from '../../interfaces/vessel-tank-request.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';

export const VesselTankActions = createActionGroup({
  source: 'Vessel Tank',
  events: {
    initialize_Vessel_Tanks: props<{
      vesselId: string;
    }>(),
    load_Vessel_Tanks_Lists: props<{
      vesselId: string;
    }>(),
    load_Vessel_Tanks_Lists_Success: props<{ vesselTanks: VesselTank[] }>(),
    load_Vessel_Tanks_Lists_Failure: errorProps(),

    load_Vessel_Tank_Types: emptyProps(),
    load_Vessel_Tank_Types_Success: props<{ tankTypes: TankType[] }>(),
    load_Vessel_Tank_Types_Failure: errorProps(),

    load_Vessel_Bulk_Types: emptyProps(),
    load_Vessel_Bulk_Types_Success: props<{ bulkTypes: BulkType[] }>(),
    load_Vessel_Bulk_Types_Failure: errorProps(),

    remove_Vessel_Tank: props<{ vesselTankId: string, vesselId: string }>(),
    remove_Vessel_Tank_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    remove_Vessel_Tank_Failure: errorProps(),

    add_Vessel_Tank: props<{ vesselTank: VesselTankRequest, vesselId: string; }>(),
    add_Vessel_Tank_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    add_Vessel_Tank_Failure: errorProps(),

    edit_Vessel_Tank: props<{
      vesselTankId: string;
      vesselTank: VesselTankRequest;
      vesselId: string;
    }>(),
    edit_Vessel_Tank_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    edit_Vessel_Tank_Failure: errorProps(),

    export_Vessel_Tanks: props<{
      vesselId: string;
    }>(),
    export_Vessel_Tanks_Success: emptyProps(),
    export_Vessel_Tanks_Failure: errorProps(),

    change_visibility_add_edit: props<{
      vesselTank: VesselTank | null;
      visible: boolean;
    }>(),
  },
});
