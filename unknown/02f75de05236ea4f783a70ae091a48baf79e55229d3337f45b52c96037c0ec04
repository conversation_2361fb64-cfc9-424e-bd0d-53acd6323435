import {
  Component,
  inject,
  effect,
  ChangeDetectionStrategy,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgF<PERSON>, NgIf, CommonModule } from '@angular/common';
import moment from 'moment';
import { OnlyDigitsDirective } from '../../../../../../libs/components/src/lib/directives/only-digits.directive';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  timeAfter,
  timeBefore,
} from 'libs/components/src/lib/functions/utility.functions';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { CalendarModule } from 'primeng/calendar';
import { repeatDialogFeature } from '../../store/features/repeat-dialog.feature';
import { RepeatDialogActions } from '../../store/actions/repeat-dialog.actions';
import { RepeatDialogData } from '../../shared/types/repeat-dialog-data.interface';
import { stripTimezoneOffset } from '../../../../../../libs/services/src/lib/services/functions/convert-date.utils';
import { InputTextModule } from 'primeng/inputtext';

@Component({
  selector: 'lookahead-repeat-dialog',
  templateUrl: './repeat-dialog.component.html',
  styleUrls: ['./repeat-dialog.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf,
    NgFor,
    FormsModule,
    CommonModule,
    OnlyDigitsDirective,
    DialogModule,
    DividerModule,
    CalendarModule,
    InputTextModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RepeatDialogComponent {
  store = inject(Store);
  router = inject(Router);
  snackBar = inject(MatSnackBar);

  isVisibleRepeatDialog = this.store.selectSignal(
    repeatDialogFeature.selectVisible
  );
  dialogData = this.store.selectSignal(repeatDialogFeature.selectData);

  startControl = new FormControl('');
  repeatControl = new FormControl('');
  endControl = new FormControl('');
  repeatEvery: number = 1;
  repeatUnit: string = 'week';
  weeklyPatternError = false;
  daysOfWeek = [
    { label: 'S', value: 'Sunday' },
    { label: 'M', value: 'Monday' },
    { label: 'T', value: 'Tuesday' },
    { label: 'W', value: 'Wednesday' },
    { label: 'T', value: 'Thursday' },
    { label: 'F', value: 'Friday' },
    { label: 'S', value: 'Saturday' },
  ];
  weekPattern: string[] = [];
  today = moment().startOf('day').toDate();
  timeAfter: Date | null = null;
  timeBefore: Date | null = null;
  minDifference = 1;

  form = new FormGroup({
    startDate: new FormControl<Date | null>(null, [Validators.required]),
    endDate: new FormControl<Date | null>(null, [Validators.required]),
    repeatEveryNumberOfWeeks: new FormControl<number>(1, [
      Validators.required,
      Validators.min(1),
    ]),
    weeklyPattern: new FormControl<string[]>([]),
  });

  constructor() {
    effect(() => {
      const data = this.dialogData();
      if (data) {
        this.form.patchValue({
          startDate: this.deserializeDate(data.startDate),
          endDate: this.deserializeDate(data.endDate),
          repeatEveryNumberOfWeeks: data.repeatEveryNumberOfWeeks || 1,
        });
        if (data.weeklyPattern) {
          this.weekPattern = data.weeklyPattern.split(',');
        }
        if (data.startDate) {
          this.changeStartDate(this.form.value.startDate!);
        }
        if (data.endDate) {
          this.changeEndDate(this.form.value.endDate!);
        }
      }
    });
  }

  private deserializeDate(dateString: string | null): Date | null {
    return dateString ? new Date(dateString) : null;
  }

  hideDialog() {
    this.form.reset();
    this.weekPattern = [];
    this.store.dispatch(
      RepeatDialogActions.changeVisibilityRepeatDialog({ visible: false })
    );
  }

  toggleDay(day: string) {
    if (this.weekPattern.includes(day)) {
      this.weekPattern = this.weekPattern.filter((d) => d !== day);
    } else {
      this.weekPattern.push(day);
    }
    this.weekPattern.length === 0
      ? (this.weeklyPatternError = true)
      : (this.weeklyPatternError = false);
  }

  changeStartDate(value: Date | null | undefined): void {
    this.timeBefore = timeBefore(value!, { min: this.minDifference });
    this.form.controls.endDate.setValidators([
      Validators.required,
      Validators.min(value!.getTime()),
    ]);
    this.form.controls.endDate.updateValueAndValidity();
  }

  changeEndDate(value: Date): void {
    this.timeAfter = timeAfter(value, { min: this.minDifference - 1 });
    this.form.controls.startDate.setValidators([
      Validators.required,
      Validators.max(value.getTime()),
    ]);
    this.form.controls.startDate.updateValueAndValidity();
  }

  repeatedDays(): string {
    if (this.weekPattern.length === 0) {
      return '';
    }
    const weeklyPatternNames = this.daysOfWeek
      .filter((day) => this.weekPattern.includes(day.value))
      .map((day) => day.value)
      .join(', ');

    return `Occurs every ${weeklyPatternNames} starting ${
      this.form.value.startDate !== null
        ? moment(this.form.value.startDate?.toISOString()).format('DD/MM/yyyy')
        : ''
    }`;
  }

  save(): void {
    if (this.form.invalid || this.weekPattern.length === 0) {
      this.weekPattern.length === 0
        ? (this.weeklyPatternError = true)
        : (this.weeklyPatternError = false);
      this.form.markAllAsTouched();
      return;
    }

    const startDate = stripTimezoneOffset(this.form.value.startDate!);
    const endDate = stripTimezoneOffset(this.form.value.endDate!);

    const serializeDate = (date: Date | null): string | null => {
      return date instanceof Date ? date.toISOString() : null;
    };

    const data: RepeatDialogData = {
      startDate: serializeDate(startDate),
      endDate:
        serializeDate(endDate) ||
        stripTimezoneOffset(
          new Date(new Date().getFullYear(), 11, 31)
        ).toISOString(),
      repeatEveryNumberOfWeeks: this.form.value.repeatEveryNumberOfWeeks || 0,
      weeklyPattern: this.weekPattern ? this.weekPattern.join(',') : '',
    };

    this.store.dispatch(
      RepeatDialogActions.closeRepeatDialog({ result: data })
    );
    this.form.reset();
    this.weekPattern = [];

    this.snackBar.open('Series Detected: Start and end dates added', 'Close', {
      duration: 3000,
      panelClass: 'snackbar--success',
    });
  }
}
