<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisibleRepeatDialog()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '800px' }"
>
  <ng-template pTemplate="header">
    <div class="header">Set Recurrence</div>
  </ng-template>
  <ng-template pTemplate="body">
    <form [formGroup]="form">
      <div class="repeat__form mt-12">
        <label class="label">Start</label>
        <p-calendar
          formControlName="startDate"
          [showIcon]="true"
          [minDate]="today"
          [maxDate]="timeAfter!"
          dateFormat="dd/mm/yy"
          placeholder="DD/MM/YYYY"
          appendTo="body"
          (onSelect)="changeStartDate($event)"
        >
        </p-calendar>
      </div>
      <div class="mt-5 repeat__form repeat-every-container">
        <label class="mr-16">Repeat Every</label>
        <div class="repeat-container">
          <input
            pInputText
            class="form-inputs"
            type="number"
            formControlName="repeatEveryNumberOfWeeks"
          />
          <small
            class="validation-control-error"
            *ngIf="
              form.controls.repeatEveryNumberOfWeeks.hasError('required') &&
              form.controls.repeatEveryNumberOfWeeks.touched
            "
          >
            Number is required
          </small>
        </div>
        <p class="repeat-text">Week(s)</p>
      </div>
      <div class="days-wrapper">
        <div class="days-container">
          <span
            class="days"
            *ngFor="let day of daysOfWeek"
            (click)="toggleDay(day.value)"
            [ngClass]="{ selected: weekPattern.includes(day.value) }"
          >
            {{ day.label }}
          </span>
        </div>
        <small class="validation-control-error" *ngIf="weeklyPatternError">
          Days of week selection is required
        </small>
      </div>
      <div class="repeat__form">
        <label class="label">End</label>
        <p-calendar
          formControlName="endDate"
          [showIcon]="true"
          [minDate]="timeBefore || today"
          dateFormat="dd/mm/yy"
          hourFormat="24"
          appendTo="body"
          placeholder="DD/MM/YYYY"
          (onSelect)="changeEndDate($event)"
        >
        </p-calendar>
      </div>
      <h4>{{ repeatedDays() }}</h4>
    </form>
  </ng-template>

  <ng-template pTemplate="footer">
    <button pButton class="btn-tertiary" (click)="hideDialog()">Cancel</button>
    <button pButton class="btn-primary" (click)="save()">Save</button>
  </ng-template>
</p-dialog>
