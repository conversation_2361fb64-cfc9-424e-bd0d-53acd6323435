<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ hireStatement() ? 'Edit' : 'Add' }} Hire Statement
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Type</label>
          <p-dropdown
            [options]="typeList"
            [filter]="true"
            formControlName="type"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.type?.invalid && controls.type?.touched"
          >
            Type is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Day Rate</label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="dayRate"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.dayRate?.invalid && controls.dayRate?.touched"
          >
            {{
              controls.dayRate?.hasError('required')
                ? 'Day rate is required.'
                : 'Day Rate should be more than 0'
            }}
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label for="deliveryDate" class="fs-14">Delivery Date</label>
          <p-calendar
            [showTime]="true"
            [showSeconds]="false"
            appendTo="body"
            formControlName="deliveryDate"
            dateFormat="dd/mm/yy"
            (onClear)="controls.deliveryDate?.setValue(null)"
            [maxDate]="controls.redeliveryDate?.value!"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="
              controls.deliveryDate?.invalid && controls.deliveryDate?.touched
            "
          >
            Delivery date is required
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label for="redeliveryDate" class="fs-14">Redelivery Date</label>
          <p-calendar
            [showTime]="true"
            [showSeconds]="false"
            appendTo="body"
            formControlName="redeliveryDate"
            dateFormat="dd/mm/yy"
            (onClear)="controls.redeliveryDate?.setValue(null)"
            [minDate]="controls.deliveryDate?.value!"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="
              controls.redeliveryDate?.invalid &&
              controls.redeliveryDate?.touched
            "
          >
            Redelivery date is required
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Delivery Place</label>
          <input pInputText type="text" formControlName="deliveryPlace" />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deliveryPlace?.invalid && controls.deliveryPlace?.touched
            "
          >
            Delivery place is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Redelivery Place</label>
          <input pInputText type="text" formControlName="redeliveryPlace" />
          <small
            class="validation-control-error"
            *ngIf="
              controls.redeliveryPlace?.invalid &&
              controls.redeliveryPlace?.touched
            "
          >
            Redelivery place is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 align-items-center gap-4">
          <p-inputSwitch formControlName="isOnHire" />
          <label class="fs-14">
            Is {{ controls.isOnHire?.value ? 'On' : 'Off' }} Hire
          </label>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ hireStatement() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
