import { Async<PERSON>ip<PERSON>, CommonModule, Ng<PERSON>lass, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  Output,
  ViewChild,
} from '@angular/core';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Store } from '@ngrx/store';
import { CheckboxModule } from 'primeng/checkbox';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { filePatternValidator } from '../../functions/file-helpers';
import { ActivatedRoute } from '@angular/router';
import { VoyageAttachmentType } from 'libs/services/src/lib/services/voyages/enums/voyage-attachment-type.enum';

@Component({
  selector: 'attached-files',
  templateUrl: './attached-files.component.html',
  styleUrls: ['./attached-files.component.scss'],
  standalone: true,
  imports: [
    AsyncPipe,
    NgIf,
    NgFor,
    NgClass,
    FileUploadModule,
    ProgressSpinnerModule,
    CommonModule,
    CheckboxModule,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class AttachedFilesComponent implements OnChanges {
  store = inject(Store);

  @Input() entityId = '';
  @Input() attachments: any[] = [];
  @Input() transportRequest = false;
  @Input() uploadFileFn?: (file: File, entityId: string) => void = () => { };
  @Input() uploadFileBulkFn?: (file: File[], entityId: string) => void =
    () => { };
  @Input() removeFileFn: (file: any, entityId: string) => void = () => { };
  @Input() downloadFileFn: (file: any, entityId: string) => void = () => { };
  @Input() isLoading = false;
  @Input() gridTemplateColumns: string = 'repeat(3, 1fr)';
  @Input() viewOnly: boolean = false;
  @Input() filesAccept: string = '*/*';
  @Input() maxFileCount: number | undefined = undefined;
  @Input() bulkUpload: boolean = false;
  @Input() canManage: boolean = true;
  @Input() attachmentTypes: VoyageAttachmentType[] = [];
  @Output() fileChecked = new EventEmitter<{ file: any; checked: boolean }>();
  @ViewChild('fileUpload') fileUpload!: FileUpload;
  form: FormGroup = new FormGroup({});
  snackBar = inject(MatSnackBar);
  route = inject(ActivatedRoute)

  cargoReportAttachmentType = VoyageAttachmentType.CargoReport;
  
  ngOnChanges() {
    Object.keys(this.form.controls).forEach((key) => {
      if (!this.attachments.find((att) => att.fileName === key)) {
        this.form.removeControl(key);
      }
    });

    this.attachments.forEach((attachment) => {
      if (!this.form.contains(attachment.documentName || attachment.fileName)) {
        this.form.addControl(
          attachment.documentName || attachment.fileName,
          new FormControl<boolean>(false)
        );
      }

      if (
        (attachment.transportRequestCargoId ||
          attachment.transportRequestBulkCargoId ||
          attachment.transportRequestmaterialDetailId) &&
        (attachment.documentName || attachment.fileName)
      ) {
        this.form.controls[
          attachment.documentName || attachment.fileName
        ].setValue(true);
      }
    });
  }

  clearFiles(): void {
    this.fileUpload.clear();
  }

  emitCheckedFile(attachment: any, isChecked: boolean) {
    this.fileChecked.emit({ file: attachment, checked: isChecked });
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  validateSelectedFiles(attachments: File[] | FileList) {
    if (
      typeof this.maxFileCount === 'number' &&
      this.maxFileCount - attachments.length < 0
    ) {
      this.snackBar.open(`File max count is 10`, 'Close', {
        duration: 3000,
        panelClass: 'snackbar--error',
      });
      this.clearFiles();
      return false;
    }
    return true;
  }

  validateSingleFile(file: File) {
    const pattern = '(.*?).(pdf|doc|docx|xls|xlsx|jpg|png|gif|mp4|txt)$';
    if (!filePatternValidator(file, pattern)) {
      this.snackBar.open(
        'Unsupported file format. Please upload a .pdf, .doc, .docx, .xls, .xlsx, .jpg, .png, .gif, .mp4 or .txt file.',
        'Close',
        {
          duration: 3000,
          panelClass: 'snackbar--error',
        }
      );
      return;
    }

    if (file.size > 510000000) {
      this.snackBar.open(`${file.name} exceeds 500MB`, 'Close', {
        duration: 3000,
        panelClass: 'snackbar--error',
      });
      return;
    }
    if (file.size >= 210000000 && file.size <= 510000000) {
      this.snackBar.open(`Large file size please wait...`, 'Close', {
        duration: 6000,
        panelClass: 'snackbar--warning',
      });
    }
    const fileExists = this.attachments.some(
      (attachment) => attachment.documentName === file.name
    );

    const filenameExists = this.attachments.some(
      (attachment) => attachment.fileName === file.name
    );

    if (fileExists || filenameExists) {
      this.snackBar.open(
        `File: ${file.name}, already exists on this Transport Request.`,
        'Close',
        {
          duration: 3000,
          panelClass: 'snackbar--error',
        }
      );
      return;
    }
    return true;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    const attachments: FileList | undefined = event.dataTransfer?.files;
    if (this.canManage && attachments && this.validateSelectedFiles(attachments)) {
      if (this.bulkUpload) {
        this.uploadFilesBulk(Array.from(attachments));
      } else {
        Array.from(attachments).forEach((attachment: File) => {
          this.uploadFile(attachment);
        });
      }
    }
  }

  selectNewAttachment(event: any) {
    const attachments = event.files as FileList;
    if (attachments && this.validateSelectedFiles(attachments)) {
      if (this.bulkUpload) {
        this.uploadFilesBulk(Array.from(attachments));
      } else {
        Array.from(attachments).forEach((attachment: File) => {
          this.uploadFile(attachment);
        });
      }
    }
  }

  removeSelectedFile(file: any) {
    this.snackBar.open(`File has been removed successfully`, 'Close', {
      duration: 3000,
      panelClass: 'snackbar--success',
    });
    this.removeFileFn(file, this.entityId);
  }

  downloadSelectedFile(file: any) {
    this.downloadFileFn(file, this.entityId);
  }

  uploadFile(file: File) {
    if (this.validateSingleFile(file)) {
      this.uploadFileFn?.(file, this.entityId);
    }
  }

  uploadFilesBulk(files: File[]) {
    if (files.every((file) => this.validateSingleFile(file))) {
      this.uploadFileBulkFn?.(files, this.entityId);
    }
  }
}
