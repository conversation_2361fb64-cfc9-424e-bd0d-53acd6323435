<p-dialog
  [draggable]="true"
  [closable]="false"
  [modal]="true"
  [(visible)]="dialogVisible"
  [style]="{ width: '1040px'}"
>

  <ng-template pTemplate="header">
    <div class="header">Attachments</div>
  </ng-template>
  <ng-template pTemplate="content">
    <div
      *ngIf="isLoading"
      class="d-flex justify-content-center"
    >
      <p-progressSpinner [styleClass]="'spinner-style'"></p-progressSpinner>
    </div>
    <form
      *ngIf="!isLoading"
      class="mt-12"
      [formGroup]="form"
    >
      <div class="mt-20">
        <attached-files
          [entityId]="entityId"
          [attachments]="attachments"
          [uploadFileFn]="uploadFileFn"
          [uploadFileBulkFn]="uploadFileBulkFn"
          [bulkUpload]="bulkUpload"
          [removeFileFn]="removeFileFn"
          [downloadFileFn]="downloadFileFn"
          [transportRequest]="transportRequest"
          (fileChecked)="fileChecked($event)"
          [viewOnly]="viewOnly"
          [filesAccept]="filesAccept"
          [maxFileCount]="maxFileCount"
          [gridTemplateColumns]="gridTemplateColumns"
          [attachmentTypes]="attachmentTypes || []"
        >
        </attached-files>
      </div>
    </form>

  </ng-template>

  <ng-template pTemplate="footer">
    <button
      class="btn-tertiary"
      type="button"
      (click)="hideDialog()"
    >
      Close
    </button>
    <button
      *ngIf="!viewOnly && withSave"
      class="btn-primary"
      type="button"
      [disabled]="transportRequest && !attachmentsToSave.length"
      (click)="save($event)"
    >
      Save
    </button>
  </ng-template>
</p-dialog>