import { inject, Injectable } from '@angular/core';
import { EMPTY, Observable } from 'rxjs';
import { Cargo } from './interfaces/cargoes.interface';
import { HttpClient } from '@angular/common/http';
import { Location } from './interfaces/location.interface';
import { Vendor } from './interfaces/vendor.interface';
import { CargoUnitType } from './interfaces/cargo-unit-type.interface';
import { MaintenanceCargoFilterModel } from './interfaces/maintenance-cargo-filter-model';

@Injectable({ providedIn: 'root' })
export class CargoService {
  private readonly http = inject(HttpClient);

  loadCargos(
    filterModel: MaintenanceCargoFilterModel
  ): Observable<Cargo[]> {
    const requestBody = {
      ...filterModel,
      returnApprovedOnly: false,
    };
  
    return this.http.post<Cargo[]>(`/api/cargo/filter`, requestBody);
  }

  loadCargosList(): Observable<Cargo[]> {
    return this.http.get<Cargo[]>('/api/Cargo');
  }

  loadCargosIncludingAdhoc(): Observable<Cargo[]> {
    return this.http.get<Cargo[]>('/api/Cargo/getCargosIncludingAdhoc');
  }

  getCargoDescriptions(): Observable<CargoUnitType[]> {
    return this.http.get<CargoUnitType[]>('/api/Cargo/getCargoDescriptions');
  }

  loadCargosListByLocation(
    locationId: string | undefined
  ): Observable<Cargo[]> {
    if (locationId) {
      return this.http.get<Cargo[]>(`/api/Cargo/bylocation/${locationId}`);
    }
    return EMPTY;
  }

  loadCargoesByTRC(
    transportRequestId: string | undefined
  ): Observable<Cargo[]> {
    if (transportRequestId)
      return this.http.get<Cargo[]>(`/api/Cargo/byTR/${transportRequestId}`);
    return EMPTY;
  }

  removeCargo(id: string): Observable<Cargo> {
    return this.http.delete<Cargo>(`/api/Cargo/${id}`);
  }

  addCargo(Cargo: Cargo): Observable<Cargo> {
    return this.http.post<Cargo>('/api/Cargo', Cargo);
  }

  editCargo(id: string, Cargo: Cargo): Observable<Cargo> {
    return this.http.put<Cargo>(`/api/Cargo/${id}`, Cargo);
  }

  loadLocationList(): Observable<Location[]> {
    return this.http.get<Location[]>('/api/location');
  }

  approveCargo(id: string): Observable<any> {
    return this.http.post<Cargo>(`/api/Cargo/approveCargo/${id}`, {});
  }

  enableDisableCargo(id: string): Observable<Cargo> {
    return this.http.post<Cargo>(`/api/Cargo/enableDisable/${id}`, {});
  }

  loadVendors(): Observable<Vendor[]> {
    return this.http.get<Vendor[]>('/api/Vendor');
  }
}
