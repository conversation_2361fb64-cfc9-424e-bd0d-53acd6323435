import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { Actions } from '@ngrx/effects';

import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { PanelModule } from 'primeng/panel';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { HireRequest } from 'libs/services/src/lib/services/contain/interfaces/hire-request.interface';
import { InputTextModule } from 'primeng/inputtext';
import { DatePipe } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { HireRequestconfirmDialogComponent } from '../hire-request-confirm-dialog/hire-request-confirm.dialog';
import { HireRequestEditDialogComponent } from '../hire-request-edit-dialog/hire-request-edit.dialog';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestAllocatedUnitsTable } from "../hire-request-allocated-units-table/hire-request-allocated-units.table";
import { CargoesActions } from 'libs/services/src/lib/services/maintenance/store/actions/cargoes.actions';
import { ConfirmationService } from 'primeng/api';

@Component({
  selector: 'contain-hire-request-details-dialog',
  standalone: true,
  imports: [
    NgIf,
    DialogModule,
    DividerModule,
    PanelModule,
    CheckboxModule,
    FormsModule,
    InputTextModule,
    DatePipe,
    ButtonModule,
    InputTextareaModule,
    HireRequestconfirmDialogComponent,
    HireRequestEditDialogComponent,
    HireRequestAllocatedUnitsTable
],
  templateUrl: './hire-request-details.dialog.html',
  styleUrls: ['./hire-request-details.dialog.scss'],
})
export class HireRequestDetailsDialogComponent implements OnInit {
  @Input() dialogVisible: boolean = false;
  @Input() hireRequestInput!: HireRequest;
  @Input() isConfirmedHireRequestsList!: boolean;
  @Input() isHireRequestWaitingList = false;
  @Output() dialogToggle = new EventEmitter<void>();
  private readonly confirmationService = inject(ConfirmationService);

  store = inject(Store);
  action = inject(Actions);

  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);
  hireRequestSignal = this.store.selectSignal(hireRequestFeature.selectHireRequest);

  confirmDialogVisible = false;
  editDialogVisible = false;
  allocatedUnitsModified = false;

  ngOnInit(): void {
    if(!this.isHireRequestWaitingList) {
      this.store.dispatch(CargoesActions.load_Cargos_Including_Adhoc());
    }
  }

  allocatedUnitsUpdated(modified: boolean){
    if(modified) {
      this.allocatedUnitsModified = modified;
    }
  }

  hireRequest(): HireRequest {
    return this.hireRequestSignal() ? this.hireRequestSignal()! : this.hireRequestInput;
  }

  hideDialog() {
    if(this.allocatedUnitsModified){
      this.store.dispatch(HireRequestActions.load_Hire_Requests_List({ filterModel: this.filterModel() }));
    }
    this.dialogToggle.emit();
  }

  hideConfirmDialog() {
    this.confirmDialogVisible = false;
  }

  confirmHireRequest() {
    this.confirmDialogVisible = true;
  }

  editHireRequest(hireRequest: HireRequest) {
    this.editDialogVisible = true;
  }

  hideEditDialog() {
    this.editDialogVisible = false;
  }

  cancelHireRequest() {
    this.store.dispatch(
      HireRequestActions.cancel_Hire_Request({
        hireRequestId: this.hireRequest().hireRequestId,
        filterModel: this.filterModel(),
      })
    );
    this.hideDialog();
  }

  deleteHireRequest() {
    this.store.dispatch(
      HireRequestActions.delete_Hire_Request({
        hireRequestId: this.hireRequest().hireRequestId,
        filterModel: this.filterModel()
      })
    );
    this.hideDialog();
  }

  onDelete(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to delete this Hire Request ?',
      header: 'Delete',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      accept: () => {
        this.deleteHireRequest();
      },
    });
  }

  onCancel(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to cancel this Hire Request ?',
      header: 'Cancellation',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Yes',
      rejectLabel: 'No',
      accept: () => {
        this.cancelHireRequest();
      },
    });
  }
}
