import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Ng<PERSON>lass, NgForOf, NgIf } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DomSanitizer } from '@angular/platform-browser';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { CardModule } from 'primeng/card';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { Environment } from 'env';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { VesselActions } from 'libs/services/src/lib/services/vessels/store/actions/vessels.actions';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { CountryService } from 'libs/services/src/lib/services/country.service';
import { maxLength } from 'libs/components/src/lib/validators/maxLength';
import { UploadImgComponent } from 'libs/components/src/lib/components/upload-img/upload-img.component';
import { maxImageSize } from 'libs/components/src/lib/validators/maxImageSize';
import { fileTypes } from 'libs/components/src/lib/validators/fileTypes';
import { PageMode } from 'libs/components/src/lib/enums/page-mode.enum';
import { unitsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';

@Component({
  selector: 'lha-vessel-create-edit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf,
    NgForOf,
    UploadImgComponent,
    CardModule,
    NgClass,
    InputSwitchModule,
    InputTextModule,
    InputNumberModule,
    DropdownModule,
    CalendarModule,
  ],
  templateUrl: './vessel-create-edit.component.html',
})
export class VesselCreateEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly environment = inject(Environment);
  private sanitizer = inject(DomSanitizer);
  private readonly store = inject(Store);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly action = inject(Actions);

  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  countryService = inject(CountryService);
  countryList = this.countryService.getCountryList();
  router = inject(Router);
  vesselId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['vesselId'];
  units = this.store.selectSignal(unitsFeature.selectUnits);
  maxImgSize = 1024 * 1024 * 2;
  imgControl = new FormControl<string | Event | null>('', [
    maxImageSize(this.maxImgSize),
    fileTypes(['image/png', 'image/jpeg']),
  ]);
  url = this.environment.apiUrl;
  form = new FormGroup({
    name: new FormControl<string>('', [Validators.required]),
    imo: new FormControl<number | null>(null, [Validators.required]),
    country: new FormControl<string>(''),
    construction: new FormControl<Date | ''>(''),
    mmsi: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      maxLength(9),
    ]),
    vesselOwner: new FormControl<string>('', [Validators.required]),
    dpClass: new FormControl<number | null>(null),
    fireFightClass: new FormControl<number | null>(null),
    length: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    width: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    draft: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    grossTonnage: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    deadWeight: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    netTonnage: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    deckLengthValue: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    deckCapacity: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    eori: new FormControl<number | null>(null, [greaterThan(0)]),
    roundedSafeHaven: new FormControl<boolean>(false, []),
    errv: new FormControl<boolean>(false, []),
    inactive: new FormControl<boolean>(false, []),
    lengthUnitName: new FormControl<string>('', [Validators.required]),
    deckLengthUnitName: new FormControl<string>('', [Validators.required]),
    widthUnitName: new FormControl<string>('', [Validators.required]),
    draftUnitName: new FormControl<string>('', [Validators.required]),
    deckCapacityUnitName: new FormControl<string>('', [Validators.required]),
    grossTonnageUnitName: new FormControl<string>('', [Validators.required]),
    netTonnageUnitName: new FormControl<string>('', [Validators.required]),
    deadWeightUnitName: new FormControl<string>('', [Validators.required]),
    deckWidthValue: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    deckWidthUnitName: new FormControl<string>('', [Validators.required]),
    picture: new FormControl<string | null>(null, []),
  });

  initialForm = this.form.value as Vessel;

  controls = {
    inactive: this.form.get('inactive'),
    imo: this.form.get('imo'),
    length: this.form.get('length'),
    lengthUnitName: this.form.get('lengthUnitName'),
    name: this.form.get('name'),
    deckLengthValue: this.form.get('deckLength'),
    deckLengthUnitName: this.form.get('deckLengthUnitName'),
    country: this.form.get('country'),
    widthUnitName: this.form.get('widthUnitName'),
    width: this.form.get('width'),
    construction: this.form.get('construction'),
    deckWidthValue: this.form.get('deckWidth'),
    deckWidthUnitName: this.form.get('deckWidthUnitName'),
    mmsi: this.form.get('mmsi'),
    draft: this.form.get('draft'),
    draftUnitName: this.form.get('draftUnitName'),
    vesselOwner: this.form.get('vesselOwner'),
    deckCapacity: this.form.get('deckCapacity'),
    deckCapacityUnitName: this.form.get('deckCapacityUnitName'),
    grossTonnage: this.form.get('grossTonnage'),
    grossTonnageUnitName: this.form.get('grossTonnageUnitName'),
    netTonnage: this.form.get('netTonnage'),
    netTonnageUnitName: this.form.get('netTonnageUnitName'),
    eori: this.form.get('eori'),
    deadWeight: this.form.get('deadWeight'),
    deadWeightUnitName: this.form.get('deadWeightUnitName'),
  };

  dpClassList = [
    {
      name: '0',
      value: 0,
    },
    {
      name: '1',
      value: 1,
    },
    {
      name: '2',
      value: 2,
    },
    {
      name: '3',
      value: 3,
    },
  ];

  firefightClassList = [
    {
      name: '0',
      value: 0,
    },
    {
      name: '1',
      value: 1,
    },
    {
      name: '2',
      value: 2,
    },
  ];

  pageMode = PageMode.Create;
  mode = PageMode;
  today = new Date();

  ngOnInit(): void {
    this.store.dispatch(VesselActions.init_vessel_add_edit());

    this.action
      .pipe(ofType(VesselActions.load_Vessel_Picture_Success))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ vesselPicture }) => {
        const picture = this.sanitizer.bypassSecurityTrustUrl(
          URL.createObjectURL(vesselPicture)
        );
        this.imgControl.patchValue(picture as string);
        this.cdr.markForCheck();
      });

    if (this.activatedRoute.snapshot.data['pageMode'] === PageMode.Create) {
      this.pageMode = PageMode.Create;
    } else {
      this.store.dispatch(
        VesselActions.load_Vessel({ vesselId: this.vesselId })
      );
      if (this.activatedRoute.snapshot.queryParams['isView'] === 'false') {
        this.edit();
      } else {
        this.view();
      }
    }

    this.action
      .pipe(
        ofType(VesselActions.load_Vessel_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ vessel }) => {
        this.initialForm = vessel;
        this.patchForm(vessel);
      });

    this.action
      .pipe(
        ofType(VesselActions.edit_Vessel_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.view();
      });
  }

  cancel(): void {
    this.patchForm(this.initialForm);
    if (this.pageMode === PageMode.Create) {
      this.router.navigate(['/vessels']);
    } else {
      this.view();
    }
  }

  edit(): void {
    this.pageMode = PageMode.Edit;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { isView: false },
    });
  }

  view() {
    this.pageMode = PageMode.View;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { isView: true },
    });
  }

  private patchForm(vessel: Vessel): void {
    this.form.patchValue({
      ...vessel,
      construction: vessel.construction ? new Date(vessel.construction) : null,
    });
    if (vessel.vesselPictureId) {
      this.store.dispatch(
        VesselActions.load_Vessel_Picture({
          vesselPictureId: vessel.vesselPictureId,
        })
      );
    }
  }

  save(): void {
    if (this.form.invalid || this.imgControl.invalid) {
      this.form.markAllAsTouched();
      this.imgControl.markAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      hasImage: !!this.imgControl.value,
    } as Vessel;

    if (this.pageMode === PageMode.Create) {
      this.store.dispatch(
        VesselActions.add_Vessel({ vessel: model, img: this.imgControl.value })
      );
    } else {
      this.store.dispatch(
        VesselActions.edit_Vessel({
          vessel: model,
          vesselId: this.vesselId,
          img: this.imgControl.value,
        })
      );
    }
  }
}
