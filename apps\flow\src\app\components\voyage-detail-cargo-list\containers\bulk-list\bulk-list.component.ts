import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  computed,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { BulkTableFields } from 'apps/flow/src/app/shared/enums';
import { Lookups } from 'apps/flow/src/app/shared/helpers/tables/lookups';
import { FilterBulkList } from 'apps/flow/src/app/shared/interfaces';
import { BulkListEditActions } from 'apps/flow/src/app/store/actions/bulk-list-edit.actions';
import { bulkListEditFeature } from 'apps/flow/src/app/store/features/bulk-list-edit.feature';
import { vendorsFeature } from 'libs/services/src/lib/services/maintenance/store/features/vendors.feature';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { FieldType } from 'libs/components/src/lib/enums';
import { assetsLocationsFeature } from 'libs/services/src/lib/services/assets-locations/store/features';
import {
  bulkTypesFeature,
  dangerousGoodsFeature,
  siteFeature,
  unitsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';

@Component({
  standalone: true,
  selector: 'app-bulk-list',
  templateUrl: './bulk-list.component.html',
  styleUrls: ['./bulk-list.component.css'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    CheckboxModule,
    DropdownModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    InputTextModule,
    InputNumberModule,
    NgClass,
    NgIf,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkListComponent implements OnInit {
  store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  assets = this.store.selectSignal(assetsLocationsFeature.selectAssets);
  voyageData = this.store.selectSignal(flowFeature.selectVoyage);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  vendors = this.store.selectSignal(vendorsFeature.selectLocationVendorsList);
  unitList = this.store.selectSignal(unitsFeature.selectUnits);
  sites = this.store.selectSignal(siteFeature.selectSites);
  bulkTypesList = this.store.selectSignal(bulkTypesFeature.selectBulkTypes);
  dangerousGoods = this.store.selectSignal(
    dangerousGoodsFeature.selectDangerousGoodsByLocationId
  );
  listColumns = this.store.selectSignal(
    bulkListEditFeature.selectBulkTableViewModeListColumns
  );
  filteredBulkList = this.store.selectSignal(
    bulkListEditFeature.filteredBulkList
  );
  tableWidth = this.store.selectSignal(
    bulkListEditFeature.selectBulkTableWidth
  );
  tableLoading = this.store.selectSignal(bulkListEditFeature.selectIsLoading)

  assetsForFilter = computed(() => [
    {
      label: 'All',
      value: 'All',
    },
    ...this.mappedAssets(),
  ]);
  mappedAssets = computed(() =>
    this.assets().map((data) => ({
      label: data.name,
      value: data.assetId,
    }))
  );

  dropdownLookups = computed<{
    [key: string]: {
      label: string;
      value: any;
    }[];
  }>(() => {
    return {
      ...Lookups,
      vendorId: this.vendors()?.map((item) => ({
        label: item.vendorName,
        value: item.vendorId,
      })),
      assetId: this.assets()?.map((data) => ({
        label: data.name,
        value: data.assetId,
      })),
      unitName: this.unitList()?.map((data) => ({
        label: data.name,
        value: data.name,
      })),
      siteId: this.sites()?.map((data) => ({
        label: data.name,
        value: data.siteId,
      })),
      bulkTypeId: this.bulkTypesList()?.map((data) => ({
        label: data.name,
        value: data.bulkTypeId,
      })),
      dangerousGoodId: this.dangerousGoods()?.map((data) => ({
        label: data.unNo,
        value: data.dangerousGoodId,
      })),
    };
  });
  fieldType = FieldType;
  bulkTableFields = BulkTableFields;

  filterForm = new FormGroup({
    assets: new FormControl('All'),
    isCancelled: new FormControl(false),
  });

  ngOnInit() {
    this.store.dispatch(
      BulkListEditActions.initialize_Bulk_List({
        locationId: this.currentUser().locationId!,
        voyageId: this.voyageData()!.voyageId,
      })
    );

    this.filterForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.store.dispatch(
          BulkListEditActions.filter_Bulk_List({
            filter: value as FilterBulkList,
          })
        );
      });
  }
}
