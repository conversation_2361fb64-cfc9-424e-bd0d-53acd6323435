﻿namespace Lighthouse.Service.Data.Contain
{
    public class MovementMatchingService : IMovementMatchingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService userService;
        private readonly IFlowVoyageService flowVoyageService;
        private readonly IHireRequestCargoEventService hireRequestCargoEventService;
        private readonly ITimeZoneConversionService timeZoneConversionService;

        public MovementMatchingService(IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            IFlowVoyageService flowVoyageService,
            IHireRequestCargoEventService hireRequestCargoEventService,
            ITimeZoneConversionService timeZoneConversionService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            this.userService = userService;
            this.flowVoyageService = flowVoyageService;
            this.hireRequestCargoEventService = hireRequestCargoEventService;
            this.timeZoneConversionService = timeZoneConversionService;
        }

        public async Task<List<MovementMatchingModel>> GetExceptions(HireRequestFilter filterModel)
        {
            UserModel user = await this.userService.GetCurrentUser();

            Expression<Func<MovementMatching, bool>> predicate;

            if (filterModel.MovementMatchingIds != null && filterModel.MovementMatchingIds.Any())
            {
                var ids = filterModel.MovementMatchingIds;
                predicate = q => ids.Contains(q.MovementMatchingId) && !string.IsNullOrEmpty(q.ExceptionReason) && !q.IsApproved && !q.Deleted;
            }
            else
            {
                predicate = q =>
                    !string.IsNullOrEmpty(q.ExceptionReason) &&
                    !q.IsApproved &&
                    !q.Deleted &&
                    (
                        q.HireClientName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementClientName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.HireBillingAssetName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementAssetName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.HireUnit.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementUnit.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.ManifestNo.ToLower().Contains(filterModel.SearchTerm.ToLower())
                    )
                    &&
                    (q.HireRequestCargo == null || q.HireRequestCargo.Cargo.LocationId == user.LocationId)
                    &&
                    (!filterModel.From.HasValue || q.MatchedAt >= filterModel.From.Value) &&
                    (!filterModel.To.HasValue || q.MatchedAt <= filterModel.To.Value);
            }

            var matchingExceptions = await _unitOfWork.Repository<MovementMatching>()
                .Query(predicate)
                .AsNoTracking()
                .AsSplitQuery()
                .IgnoreQueryFilters()
                .Include(x => x.HireRequestCargo).ThenInclude(x => x.Cargo)
                .OrderByDescending(o => o.MatchedAt)
                .ToListAsync();

            return matchingExceptions.Select(x => _mapper.Map<MovementMatchingModel>(x)).ToList();
        }

        public async Task<List<MovementMatchingModel>> GetMatches(HireRequestFilter filterModel)
        {
            UserModel user = await this.userService.GetCurrentUser();

            Expression<Func<MovementMatching, bool>> predicate;

            if (filterModel.MovementMatchingIds != null && filterModel.MovementMatchingIds.Any())
            {
                var ids = filterModel.MovementMatchingIds;
                predicate = q => ids.Contains(q.MovementMatchingId) && (string.IsNullOrEmpty(q.ExceptionReason) || q.IsApproved) && !q.Deleted;
            }
            else
            {
                predicate = q =>
                    (string.IsNullOrEmpty(q.ExceptionReason) || q.IsApproved) &&
                    !q.Deleted &&
                    (
                        q.HireClientName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementClientName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.HireBillingAssetName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementAssetName.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.HireUnit.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.MovementUnit.ToLower().Contains(filterModel.SearchTerm.ToLower()) ||
                        q.ManifestNo.ToLower().Contains(filterModel.SearchTerm.ToLower())
                    )
                    &&
                    (q.HireRequestCargo == null || q.HireRequestCargo.Cargo.LocationId == user.LocationId)
                    &&
                    (!filterModel.From.HasValue || q.MatchedAt >= filterModel.From.Value) &&
                    (!filterModel.To.HasValue || q.MatchedAt <= filterModel.To.Value);
            }

            var matchingExceptions = await _unitOfWork.Repository<MovementMatching>()
                .Query(predicate)
                .AsNoTracking()
                .AsSplitQuery()
                .IgnoreQueryFilters()
                .Include(x => x.HireRequestCargo).ThenInclude(x => x.Cargo)
                .OrderByDescending(o => o.MatchedAt)
                .ToListAsync();

            return matchingExceptions.Select(x => _mapper.Map<MovementMatchingModel>(x)).ToList();
        }

        private async Task<List<VoyageCargo>> GetFlowVoyageCargoesCompletedOnModelDate(DateTime date, VoyageDirection direction)
        {
            return await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                    x.CargoId.HasValue &&
                    x.Voyage.VoyageDirection == direction &&
                    x.Voyage.VoyageStatus == VoyageStatus.Complete &&
                    x.Voyage.CompletedDate.HasValue &&
                    x.Voyage.CompletedDate.Value.Date == date.Date &&
                    x.AssetId.HasValue)
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Cargo)
                .Include(x => x.Vendor)
                .Include(x => x.Asset).ThenInclude(x => x.Client)
                .Include(x => x.Voyage)
                .ToListAsync();
        }

        private async Task<List<HireRequestCargo>> GetHireRequestCargoes(List<VoyageCargo> voyageCargos, DateTime date, List<Guid> petersonVendorIds)
        {
            UserModel user = await this.userService.GetCurrentUser();
            var result = new List<HireRequestCargo>();

            //if flow vendor contains word peterson, then we need to grab HireRequestCargos that are not necessarily on hire.
            foreach (var voyageCargo in voyageCargos)
            {
                result.AddRange(await _unitOfWork.Repository<HireRequestCargo>()
                    .Query(x =>
                        !x.Deleted &&
                        x.Cargo.LocationId == user.LocationId &&
                        voyageCargo.CargoId.HasValue &&
                        voyageCargo.CargoId == x.CargoId &&
                        x.OnHiredDate.HasValue &&
                        date.Date >= x.OnHiredDate.Value.Date &&
                        (!x.OffHiredDate.HasValue || date.Date <= x.OffHiredDate.Value.Date) &&
                        voyageCargo.Voyage.CompletedDate.HasValue &&
                        voyageCargo.Voyage.CompletedDate.Value >= x.OnHiredDate.Value &&
                        (!x.OffHiredDate.HasValue || voyageCargo.Voyage.CompletedDate.Value <= x.OffHiredDate.Value))
                    .AsNoTracking()
                    .AsSplitQuery()
                    .Include(x => x.Client)
                    .Include(x => x.Cargo).ThenInclude(x => x.CargoFamily)
                    .Include(x => x.Cargo).ThenInclude(x => x.CargoType)
                    .Include(x => x.Cargo).ThenInclude(x => x.CargoSize)
                    .Include(x => x.BillingAsset)
                    .Include(x => x.Vendor)
                    .IgnoreQueryFilters()
                    .ToListAsync());

            }

            return result;
        }

        public async Task<MovementMatchingResponseModel> RunMovementMatching(RunMovementMatchingModel model)
        {
            var result = new MovementMatchingResponseModel();
            var currentUser = await userService.GetCurrentUser();

            var parsedDate = await timeZoneConversionService.ConvertUtcToCurrentUserTime(model.Date);

            var petersonVendorIds = await _unitOfWork.Repository<Vendor>()
                .Query(v => v.IsPeterson)
                .Select(v => v.VendorId)
                .ToListAsync();

            //grab all Flow Voyage Cargoes completed on model Date
            var voyageCargoes = await GetFlowVoyageCargoesCompletedOnModelDate(parsedDate, model.Direction);

            //get all HireRequestCargo based on the voyageCargoes
            var hireRequestCargoes = await GetHireRequestCargoes(voyageCargoes, parsedDate, petersonVendorIds);

            var cargosNotOnHireInContainModels = new List<MovementMatchingContainHireModel>();
            var movementMatchingNotOnHireFlowModels = new List<MovementMatchingFlowModel>();

            if (voyageCargoes.Count != hireRequestCargoes.Count)
            {
                //this means that we have some voyage cargos from completed voyages that are not on hire in contain. We need to check if the vendor contains "peterson" and if so, we need to add it to the hireRequestCargoes list.
                var voyageCargoesWithPetersonVendor = voyageCargoes
                    .Where(x =>
                        petersonVendorIds.Any() &&
                            x.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                            x.VendorId.HasValue &&
                            x.Vendor != null &&
                            petersonVendorIds.Contains(x.VendorId.Value))
                    .ToList();

                if (voyageCargoesWithPetersonVendor.Count != 0)
                {
                    foreach (var voyageCargo in voyageCargoesWithPetersonVendor)
                    {
                        if(!hireRequestCargoes.Any(a => a.CargoId == voyageCargo.CargoId && a.IsHired == true))
                        {
                            var movementMatchingContainHireModel = new MovementMatchingContainHireModel { CargoNotOnHireInContain = true, CargoId = voyageCargo.CargoId.Value };
                            cargosNotOnHireInContainModels.Add(movementMatchingContainHireModel);

                            movementMatchingNotOnHireFlowModels.Add(new MovementMatchingFlowModel
                            {
                                VoyageId = voyageCargo.VoyageId.Value,
                                VoyageName = voyageCargo.Voyage.VoyageNumber,
                                VoyageCargoId = voyageCargo.VoyageCargoId,
                                CargoId = voyageCargo.CargoId,
                                MovementUnit = voyageCargo.CcuId,
                                VoyageDirection = voyageCargo.Voyage.VoyageDirection,
                                CompletedDate = voyageCargo.Voyage.CompletedDate.Value,
                                SailingDate = voyageCargo.Voyage.SailingDischargeDate,
                                MovementVendorId = voyageCargo.VendorId,
                                MovementVendorName = voyageCargo.Vendor?.VendorName,
                                MovementClientId = voyageCargo.Asset?.ClientId,
                                MovementClientName = voyageCargo.Asset?.Client?.Name,
                                MovementAssetId = voyageCargo.AssetId.Value,
                                MovementAssetName = voyageCargo.Asset?.Name,
                                CargoDescription = voyageCargo.CargoDescription
                            });
                        }                        
                    }
                }
            }

            if (hireRequestCargoes.Count != 0 || cargosNotOnHireInContainModels.Count != 0)
            {
                bool hasHireRequestCargoes = hireRequestCargoes.Count != 0;
                bool hasCargosNotOnHireInContainModels = cargosNotOnHireInContainModels.Count != 0;
                var movementMatchingFlowModels = new List<MovementMatchingFlowModel>();
                var movementMatchingContainHireModels = new List<MovementMatchingContainHireModel>();

                if (hasHireRequestCargoes)
                {
                    var cargoIdsToMatch = hireRequestCargoes.Select(x => x.CargoId).Distinct().ToList();
                    var voyageCargoesCompletedOnDate = voyageCargoes.Where(w => cargoIdsToMatch.Contains(w.CargoId.Value)).ToList();

                    if (voyageCargoesCompletedOnDate.Count != 0)
                    {
                        foreach (var voyageCargo in voyageCargoesCompletedOnDate)
                        {
                            movementMatchingFlowModels.Add(new MovementMatchingFlowModel
                            {
                                VoyageId = voyageCargo.VoyageId.Value,
                                VoyageName = voyageCargo.Voyage.VoyageNumber,
                                VoyageCargoId = voyageCargo.VoyageCargoId,
                                CargoId = voyageCargo.CargoId,
                                MovementUnit = voyageCargo.CcuId,
                                VoyageDirection = voyageCargo.Voyage.VoyageDirection,
                                CompletedDate = voyageCargo.Voyage.CompletedDate.Value,
                                SailingDate = voyageCargo.Voyage.SailingDischargeDate,
                                MovementVendorId = voyageCargo.VendorId,
                                MovementVendorName = voyageCargo.Vendor?.VendorName,
                                MovementClientId = voyageCargo.Asset?.ClientId,
                                MovementClientName = voyageCargo.Asset?.Client?.Name,
                                MovementAssetId = voyageCargo.AssetId.Value,
                                MovementAssetName = voyageCargo.Asset?.Name,
                                CargoDescription = voyageCargo.CargoDescription
                            });
                        }

                        movementMatchingContainHireModels.AddRange(hireRequestCargoes
                            .Select(x => new MovementMatchingContainHireModel
                            {
                                HireRequestId = x.HireRequestId,
                                HireRequestCargoId = x.HireRequestCargoId,
                                CargoId = x.CargoId,
                                HireUnit = x.Cargo.CCUId,
                                HireClientId = x.ClientId,
                                HireClientName = x.Client?.Name,
                                HireBillingAssetId = x.BillingAssetId,
                                HireBillingAssetName = x.BillingAsset?.Name,
                                HireVendorId = x.VendorId,
                                HireVendorName = x.Vendor?.VendorName,
                                Shipped = x.Shipped,
                                OnHiredDate = x.OnHiredDate,
                                HireUnitType = $"{x.Cargo.CargoSize?.Name} {x.Cargo.CargoType?.Name} {x.Cargo.CargoFamily?.Name}",
                                Returned = x.Returned,
                                OffHiredDate = x.OffHiredDate,
                                IsHired = x.IsHired == true
                            }).ToList());
                    }
                }

                if (hasCargosNotOnHireInContainModels)
                {
                    if (movementMatchingNotOnHireFlowModels.Count != 0)
                    {
                        foreach (var notonHireFLowModel in movementMatchingNotOnHireFlowModels)
                        {
                            if (!movementMatchingFlowModels.Contains(notonHireFLowModel))
                            {
                                movementMatchingFlowModels.Add(notonHireFLowModel);
                            }
                        }
                    }

                    if (hasCargosNotOnHireInContainModels && cargosNotOnHireInContainModels.Count != 0)
                    {
                        foreach (var notOnHireContainModel in cargosNotOnHireInContainModels)
                        {
                            if (!movementMatchingContainHireModels.Contains(notOnHireContainModel))
                            {
                                movementMatchingContainHireModels.Add(notOnHireContainModel);
                            }
                        }
                    }
                }

                var movementMatchingModels = await RunMatching(currentUser.UserId, movementMatchingFlowModels, movementMatchingContainHireModels);

                await _unitOfWork.SaveChangesAsync();

                result.Exceptions = movementMatchingModels.Where(x => x.HasException && !x.IsApproved).ToList();
                result.Matches = movementMatchingModels.Where(x => !x.HasException || x.IsApproved).ToList();
            }

            return result;
        }

        public async Task<List<MovementMatchingModel>> RunMatching(
            Guid userId,
            List<MovementMatchingFlowModel> flowModels,
            List<MovementMatchingContainHireModel> containHireModels)
        {
            var groupedFlowModels = flowModels.GroupBy(g => g.CargoId);
            var groupedContainHireModels = containHireModels.GroupBy(g => g.CargoId);

            var result = new List<MovementMatchingModel>();

            foreach (var flowGroup in groupedFlowModels)
            {
                var cargoId = flowGroup.Key;
                var containHireGroup = groupedContainHireModels.SingleOrDefault(g => g.Key == cargoId);

                if (containHireGroup != null)
                {
                    foreach (var flowModel in flowGroup)
                    {
                        var containHireModel = containHireGroup.First(f => f.CargoId == flowModel.CargoId);

                        var existingMovementMatchingRecord = await _unitOfWork.Repository<MovementMatching>()
                            .Query(q => q.VoyageCargoId == flowModel.VoyageCargoId && q.HireRequestCargoId == containHireModel.HireRequestCargoId && !q.Deleted)
                            .IgnoreQueryFilters()
                            .FirstOrDefaultAsync();

                        if (existingMovementMatchingRecord != null)
                        {
                            result.Add(_mapper.Map<MovementMatchingModel>(existingMovementMatchingRecord));
                            continue;
                        }

                        if (containHireModel.CargoNotOnHireInContain)
                        {
                            var exceptionReasons = new List<string>
                                {
                                    $"Voyage Direction is Outbound and Flow Vendor is Peterson and Cargo is not on hire - Movement Vendor: {flowModel.MovementVendorName} - Cargo: {flowModel.MovementUnit}"
                                }.ToList();

                            var exceptionReason = exceptionReasons.Any() ? string.Join("; ", exceptionReasons) : null;

                            var movementMatching = new MovementMatching
                            {
                                MovementUnit = flowModel.MovementUnit,
                                Direction = flowModel.VoyageDirection,
                                CompletedDate = flowModel.CompletedDate,
                                SailingDate = flowModel.SailingDate,
                                ManifestNo = flowModel.ManifestNo,
                                MovementVendorId = flowModel.MovementVendorId,
                                MovementVendorName = flowModel.MovementVendorName,
                                MovementClientId = flowModel.MovementClientId,
                                MovementClientName = flowModel.MovementClientName,
                                HireUnit = flowModel.MovementUnit,
                                MovementAssetId = flowModel.MovementAssetId,
                                MovementAssetName = flowModel.MovementAssetName,
                                CargoId = flowModel.CargoId.Value,
                                ExceptionReason = exceptionReason,
                                VoyageId = flowModel.VoyageId,
                                VoyageName = flowModel.VoyageName,
                                VoyageCargoId = flowModel.VoyageCargoId,
                                MatchedByUserId = userId,
                                CargoDescription = flowModel.CargoDescription
                            };

                            await _unitOfWork.Repository<MovementMatching>().CreateAsync(movementMatching);

                            var model = _mapper.Map<MovementMatchingModel>(movementMatching);

                            result.Add(model);
                        }
                        else
                        {
                            bool assetNotMatching = flowModel.MovementAssetId != containHireModel.HireBillingAssetId;
                            bool vendorNotMatching = flowModel.VoyageDirection == VoyageDirection.Outbound ?
                                false :
                                flowModel.MovementVendorId != containHireModel.HireVendorId;
                            bool shippedDateBeforeOnHiredDate = containHireModel.OnHiredDate.HasValue &&
                                                                containHireModel.Shipped.HasValue &&
                                                                (await timeZoneConversionService.ConvertUtcToCurrentUserTime(containHireModel.OnHiredDate.Value)).Date >
                                                                (await timeZoneConversionService.ConvertUtcToCurrentUserTime(containHireModel.Shipped.Value)).Date;
                            bool unitNotMatching = flowModel.CargoId != containHireModel.CargoId;
                            bool clientIdNotMatching = flowModel.MovementClientId != containHireModel.HireClientId;

                            bool isMatch = !assetNotMatching && !vendorNotMatching && !shippedDateBeforeOnHiredDate &&
                                           !unitNotMatching && !clientIdNotMatching;

                            string assetNotMatchingMessage = $"Asset not matching - Movement Asset: {flowModel.MovementAssetName} - Hire Asset: {containHireModel.HireBillingAssetName}";
                            string vendorNotMatchingMessage = $"Vendor not matching - Movement Vendor: {flowModel.MovementVendorName} - Hire Vendor: {containHireModel.HireVendorName}";
                            string shippedDateBeforeOnHiredDateMessage = containHireModel.Shipped.HasValue ?
                                    $"Shipped date was before On Hired date - Shipped: {(await timeZoneConversionService.ConvertUtcToCurrentUserTime(containHireModel.Shipped.Value)).ToString("dd/MM/yyyy")} - On-Hired: {(await timeZoneConversionService.ConvertUtcToCurrentUserTime(containHireModel.OnHiredDate.Value)).ToString("dd/MM/yyyy")}" :
                                    "Shipped Date does not have a value";
                            string unitNotMatchingMessage = $"Unit not matching - Movement Unit: {flowModel.MovementUnit} - Hire Unit: {containHireModel.HireUnit}";
                            string clientIdNotMatchingMessage = $"Client not matching - Movement Client: {flowModel.MovementClientName} - Hire Client: {containHireModel.HireClientName}";
                            string directionIsOutboundAndFlowVendorIsPetersonAndCargoNotOnHireMessage = $"Voyage Direction is Outbound and Flow Vendor is Peterson and Cargo is not on hire - Movement Vendor: {flowModel.MovementVendorName} - Hire Vendor: {containHireModel.HireVendorName} - Hire Unit: {containHireModel.HireUnit}";

                            var exceptionReasons = new List<string>
                                {
                                    assetNotMatching ? assetNotMatchingMessage : null,
                                    vendorNotMatching ? vendorNotMatchingMessage : null,
                                    shippedDateBeforeOnHiredDate ? shippedDateBeforeOnHiredDateMessage : null,
                                    unitNotMatching ? unitNotMatchingMessage : null,
                                    clientIdNotMatching ? clientIdNotMatchingMessage : null,
                                }.Where(reason => reason != null).ToList();

                            var exceptionReason = exceptionReasons.Any() ? string.Join("; ", exceptionReasons) : null;

                            var movementMatching = new MovementMatching
                            {
                                MovementUnit = flowModel.MovementUnit,
                                Direction = flowModel.VoyageDirection,
                                CompletedDate = flowModel.CompletedDate,
                                SailingDate = containHireModel.Shipped ?? flowModel.SailingDate,
                                ManifestNo = flowModel.ManifestNo,
                                MovementVendorId = flowModel.MovementVendorId,
                                MovementVendorName = flowModel.MovementVendorName,
                                MovementClientId = flowModel.MovementClientId,
                                MovementClientName = flowModel.MovementClientName,
                                HireUnit = containHireModel.HireUnit,
                                HireClientId = containHireModel.HireClientId,
                                HireClientName = containHireModel.HireClientName,
                                HireBillingAssetId = containHireModel.HireBillingAssetId,
                                HireBillingAssetName = containHireModel.HireBillingAssetName,
                                MovementAssetId = flowModel.MovementAssetId,
                                MovementAssetName = flowModel.MovementAssetName,
                                HireVendorId = containHireModel.HireVendorId,
                                HireVendorName = containHireModel.HireVendorName,
                                HireRequestId = containHireModel.HireRequestId,
                                HireRequestCargoId = containHireModel.HireRequestCargoId,
                                CargoId = containHireModel.CargoId,
                                ExceptionReason = exceptionReason,
                                VoyageId = flowModel.VoyageId,
                                VoyageName = flowModel.VoyageName,
                                VoyageCargoId = flowModel.VoyageCargoId,
                                MatchedByUserId = userId,
                                HireUnitType = containHireModel.HireUnitType,
                                OnHiredDate = containHireModel.OnHiredDate,
                                Shipped = containHireModel.Shipped,
                                Returned = containHireModel.Returned,
                                OffHiredDate = containHireModel.OffHiredDate,
                                CargoDescription = flowModel.CargoDescription
                            };

                            await _unitOfWork.Repository<MovementMatching>().CreateAsync(movementMatching);

                            var model = _mapper.Map<MovementMatchingModel>(movementMatching);

                            result.Add(model);

                            //if match, update HireRequestCargo information from the Flow Voyage and Flow VoyageCargo (auto-approve hire in contain).
                            if (isMatch)
                            {
                                movementMatching.IsApproved = true;
                                await _unitOfWork.SaveChangesAsync();
                                await UpdateContainHireRequestCargoWithFlowVoyageData(model, movementMatching, true);
                            }
                        }
                    }
                }
            }

            return result;
        }

        public async Task UpdateContainHireRequestCargoWithFlowVoyageData(MovementMatchingModel movementMatching, MovementMatching movementMatchingEntity = null, bool saveChanges = false)
        {
            var hireRequestCargo = await GetHireRequestCargo(movementMatching.HireRequestCargoId);
            var flowVoyageCargo = await GetFlowVoyageCargo(movementMatching.VoyageCargoId);

            OffshoreLocation firstOffshoreLocation = null;

            if (hireRequestCargo != null && flowVoyageCargo != null)
            {
                firstOffshoreLocation = await GetFirstOffshoreLocation(flowVoyageCargo.VoyageId.Value);

                if (flowVoyageCargo.Voyage.VoyageDirection == VoyageDirection.Outbound)
                {
                    await HandleOutboundVoyage(hireRequestCargo, flowVoyageCargo, firstOffshoreLocation);
                }
                else if (flowVoyageCargo.Voyage.VoyageDirection == VoyageDirection.Inbound)
                {
                    await HandleInboundVoyage(hireRequestCargo, flowVoyageCargo, firstOffshoreLocation);
                }

                _unitOfWork.Repository<HireRequestCargo>().Update(hireRequestCargo);
            }

            if (saveChanges)
            {
                if (movementMatchingEntity == null)
                {
                    movementMatchingEntity = await _unitOfWork.Repository<MovementMatching>()
                        .Query(x => x.MovementMatchingId == movementMatching.MovementMatchingId)
                        .FirstOrDefaultAsync();

                    if (movementMatchingEntity == null)
                    {
                        throw new Exception("Movement Matching record cannot be found.");
                    }
                }

                movementMatchingEntity.IsApproved = true;
                
                if(firstOffshoreLocation != null)
                {
                    movementMatchingEntity.ManifestNo = firstOffshoreLocation?.ManifestNumber;
                }

                _unitOfWork.Repository<MovementMatching>().Update(movementMatchingEntity);
                await _unitOfWork.SaveChangesAsync();
            }
        }

        private async Task<HireRequestCargo> GetHireRequestCargo(Guid hireRequestCargoId)
        {
            return await _unitOfWork.Repository<HireRequestCargo>()
                .Query(x => x.HireRequestCargoId == hireRequestCargoId && !x.Deleted)
                .IgnoreQueryFilters()
                .SingleOrDefaultAsync();
        }

        private async Task<VoyageCargo> GetFlowVoyageCargo(Guid voyageCargoId)
        {
            return await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageCargoId == voyageCargoId && x.AssetId.HasValue)
                .Include(x => x.Voyage)
                .Include(x => x.Vendor)
                .SingleOrDefaultAsync();
        }

        private async Task<OffshoreLocation> GetFirstOffshoreLocation(Guid voyageId)
        {
            return await _unitOfWork.Repository<OffshoreLocation>()
                .Query(x => x.VoyageId == voyageId)
                .OrderBy(x => x.CreatedDate)
                .FirstOrDefaultAsync();
        }

        private async Task HandleOutboundVoyage(HireRequestCargo hireRequestCargo, VoyageCargo flowVoyageCargo, OffshoreLocation firstOffshoreLocation)
        {
            if (flowVoyageCargo.Voyage.VoyageStatus == VoyageStatus.Complete &&
                flowVoyageCargo.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                flowVoyageCargo.Voyage.SailingDischargeDate.HasValue &&
                !hireRequestCargo.Shipped.HasValue)
            {
                hireRequestCargo.Shipped = flowVoyageCargo.Voyage.SailingDischargeDate;
            }

            if (string.IsNullOrEmpty(hireRequestCargo.ManifestOut))
            {
                hireRequestCargo.ManifestOut = firstOffshoreLocation?.ManifestNumber;
            }

            if (hireRequestCargo.Shipped.HasValue && !string.IsNullOrEmpty(hireRequestCargo.ManifestOut))
            {
                var hireRequestCargoEventUpsertModel = new HireRequestCargoEventUpsertModel
                {
                    HireRequestCargoId = hireRequestCargo.HireRequestCargoId,
                    EventDate = hireRequestCargo.Shipped.Value,
                    EventType = HireRequestCargoEventType.Shipped,
                    Details = hireRequestCargo.ManifestOut,
                };

                await hireRequestCargoEventService.CreateAsync(hireRequestCargoEventUpsertModel, false);
            }

            //update the Billing Asset and the Asset of the HireRequestCargo on Approve Movement Matching function, to be done only for Outbound voyages.
            hireRequestCargo.AssetId = flowVoyageCargo.AssetId.Value;
            hireRequestCargo.BillingAssetId = flowVoyageCargo.AssetId.Value;

            if (string.IsNullOrEmpty(hireRequestCargo.VendorOutbound))
            {
                hireRequestCargo.VendorOutbound = flowVoyageCargo.Vendor?.VendorName;
            }
        }

        private async Task HandleInboundVoyage(HireRequestCargo hireRequestCargo, VoyageCargo flowVoyageCargo, OffshoreLocation firstOffshoreLocation)
        {
            if (flowVoyageCargo.Voyage.VoyageStatus == VoyageStatus.Complete &&
                flowVoyageCargo.Voyage.VoyageDirection == VoyageDirection.Inbound &&
                flowVoyageCargo.Voyage.SailingDischargeDate.HasValue &&
                !hireRequestCargo.Returned.HasValue)
            {
                hireRequestCargo.Returned = flowVoyageCargo.Voyage.SailingDischargeDate;
            }

            if (string.IsNullOrEmpty(hireRequestCargo.ManifestIn))
            {
                hireRequestCargo.ManifestIn = firstOffshoreLocation?.ManifestNumber;
            }

            if (hireRequestCargo.Returned.HasValue && !string.IsNullOrEmpty(hireRequestCargo.ManifestIn))
            {
                var hireRequestCargoEventUpsertModel = new HireRequestCargoEventUpsertModel
                {
                    HireRequestCargoId = hireRequestCargo.HireRequestCargoId,
                    EventDate = hireRequestCargo.Returned.Value,
                    EventType = HireRequestCargoEventType.Returned,
                    Details = hireRequestCargo.ManifestIn,
                };

                await hireRequestCargoEventService.CreateAsync(hireRequestCargoEventUpsertModel, false);
            }

            if (string.IsNullOrEmpty(hireRequestCargo.VendorInbound))
            {
                hireRequestCargo.VendorInbound = flowVoyageCargo.Vendor?.VendorName;
            }
        }

        public async Task<bool> DeleteAsync(Guid movementMatchingId)
        {
            var movementMatching = await _unitOfWork.Repository<MovementMatching>()
                .Query(x => x.MovementMatchingId == movementMatchingId)
                .SingleOrDefaultAsync();

            if (movementMatching == null)
            {
                throw new Exception("Movement Matching record cannot be found.");
            }

            movementMatching.Deleted = true;
            _unitOfWork.Repository<MovementMatching>().Update(movementMatching);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
