import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { VesselTank } from './interfaces/vessel-tank.interface';
import { TankType } from '../maintenance/interfaces/tank-type.interface';
import { BulkType } from '../maintenance/interfaces/bulk-type.interface';
import { VesselTankRequest } from './interfaces/vessel-tank-request.interface';

@Injectable({
  providedIn: 'root',
})
export class VesselTankService {
  private http = inject(HttpClient);

  loadTankList(): Observable<VesselTank[]> {
    return this.http.get<VesselTank[]>('/api/vesseltank');
  }

  loadTankListByVesselId(id: string): Observable<VesselTank[]> {
    return this.http.get<VesselTank[]>(
      `/api/vesseltank/vesselid?vesselId=${id}`
    );
  }

  loadTank(id: string): Observable<VesselTank> {
    return this.http.get<VesselTank>(`/api/vesseltank/${id}`);
  }

  removeTank(id: string): Observable<VesselTank> {
    return this.http.delete<VesselTank>(`/api/vesseltank/${id}`);
  }

  addTank(tank: VesselTankRequest): Observable<VesselTank> {
    return this.http.post<VesselTank>('/api/vesseltank', tank);
  }

  editTank(id: string, tank: VesselTankRequest): Observable<VesselTank> {
    return this.http.put<VesselTank>(`/api/vesseltank/${id}`, tank);
  }

  loadTankTypes(): Observable<TankType[]> {
    return this.http.get<TankType[]>('/api/tanktype/');
  }

  loadBulkTypes(): Observable<BulkType[]> {
    return this.http.get<BulkType[]>('/api/bulktype');
  }

  exportVesselTanks(vesselId: string): Observable<ArrayBuffer> {
    return this.http.get(`/api/vesseltank/exportvesseltanks/${vesselId}`, {
      responseType: 'arraybuffer',
    });
  }
}
