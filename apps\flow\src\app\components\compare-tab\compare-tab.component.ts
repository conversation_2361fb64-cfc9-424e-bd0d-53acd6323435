import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';

import { CardModule } from 'primeng/card';
import { DropdownModule } from 'primeng/dropdown';

import { CompareStatus } from 'libs/services/src/lib/services/enums/compare-status.enum';
import { EqualObjects } from 'libs/components/src/lib/functions/equal-objects';

import { voyageCargoSnapshotFeature } from '../../store/features/voyage-cargo-snapshot.feature';
import { VoyageCargoSnapshotActions } from 'libs/services/src/lib/services/voyages/store/actions/voyage-snapshot.actions';
import { ComparePageActions } from '../../store/actions/compare-page.action';
import { CompareTableComponent } from './compare-table/compare-table.component';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BulkTableFields } from '../../shared/enums';
import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { InitializeCompareCargoInboundTable } from '../../shared/helpers/tables/compare-cargo-inbound-table';
import { InitializeCompareCargoOutboundTable } from '../../shared/helpers/tables/compare-cargo-outbound-table';
import { InitializeCompareBulksTable } from '../../shared/helpers/tables/compare-bulks-table';
import { InitializeCompareMaterialDetailsTable } from '../../shared/helpers/tables/compare-material-details-table';
import { ComparePath } from 'libs/components/src/lib/enums/compare-path.enum';

@Component({
  standalone: true,
  selector: 'flow-compare-tab',
  templateUrl: './compare-tab.component.html',
  styleUrls: ['./compare-tab.component.css'],
  imports: [CardModule, DropdownModule, CompareTableComponent, FormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CompareTabComponent implements OnInit {
  private readonly store = inject(Store);
  private readonly route = inject(ActivatedRoute);

  voyageCargoSnapshotList = this.store.selectSignal(
    voyageCargoSnapshotFeature.selectVoyageCargoSnapshotList
  );
  source = this.store.selectSignal(voyageCargoSnapshotFeature.selectSource);
  target = this.store.selectSignal(voyageCargoSnapshotFeature.selectTarget);
  sourceLoading = this.store.selectSignal(
    voyageCargoSnapshotFeature.selectSourceLoading
  );
  targetLoading = this.store.selectSignal(
    voyageCargoSnapshotFeature.selectTargetLoading
  );

  selectedSourceId = this.store.selectSignal(
    voyageCargoSnapshotFeature.selectSelectedSourceId
  );
  selectedTargetId = this.store.selectSignal(
    voyageCargoSnapshotFeature.selectSelectedTargetId
  );

  voyageData = this.store.selectSignal(flowFeature.selectVoyage);

  currentVersion = this.route.snapshot.queryParams['version'];
  path = this.route.snapshot.routeConfig?.path! as ComparePath;
  emptyMessage = this.writeEmptyMessage(this.path);

  listColumns = computed<ColumnModel[]>(() => {
    let columns: ColumnModel[] = [];
    const voyageDirection = this.voyageData()?.voyageDirection;
    switch (this.path as ComparePath) {
      case ComparePath.Cargo:
        if (voyageDirection === VoyageDirection.Inbound) {
          columns = InitializeCompareCargoInboundTable();
        } else {
          columns = InitializeCompareCargoOutboundTable();
        }
        break;
      case ComparePath.Bulks:
        columns =
          voyageDirection === VoyageDirection.Outbound
            ? InitializeCompareBulksTable()
            : InitializeCompareBulksTable().filter(
                (column) => column.field !== BulkTableFields.deliveredQuantity
              );
        break;
      case ComparePath.MaterialDetails:
        columns = InitializeCompareMaterialDetailsTable();
        break;
    }

    return columns;
  });

  sourceCargoList = computed(() => {
    if (this.source()?.content?.length) {
      return this.source()!.content.map((cargo) => {
        const index = this.target()?.content?.findIndex(
          (item) => item.voyageCargoId === cargo.voyageCargoId
        );
        if (index === undefined || index < 0) {
          return {
            ...cargo,
            compareStatus:
              this.source()?.versionNumber &&
              this.target()?.versionNumber &&
              this.target()!.versionNumber > this.source()!.versionNumber
                ? CompareStatus.Removed
                : CompareStatus.New,
          };
        } else {
          const compare = EqualObjects(cargo, this.target()!.content[index]);
          return {
            ...cargo,
            mismatchedProps: compare.mismatchedProps,
          };
        }
      });
    }

    return [];
  });

  targetCargoList = computed(() => {
    if (this.target()?.content?.length) {
      return this.target()!.content.map((cargo) => {
        const index = this.source()?.content?.findIndex(
          (item) => item.voyageCargoId === cargo.voyageCargoId
        );
        if (index === undefined || index < 0) {
          return {
            ...cargo,
            compareStatus:
              this.source()?.versionNumber &&
              this.target()?.versionNumber &&
              this.target()!.versionNumber < this.source()!.versionNumber
                ? CompareStatus.Removed
                : CompareStatus.New,
          };
        } else {
          const compare = EqualObjects(cargo, this.source()!.content[index]);
          return {
            ...cargo,
            mismatchedProps: compare.mismatchedProps,
          };
        }
      });
    }

    return [];
  });

  ngOnInit() {
    this.store.dispatch(
      ComparePageActions.initialize_Tab({
        version: this.currentVersion,
        path: this.path,
      })
    );
  }

  changeSource(voyageCargoSnapshotId: string) {
    this.store.dispatch(
      VoyageCargoSnapshotActions.get_Source_Voyage_Cargo_Snapshot({
        voyageCargoSnapshotId,
        path: this.path,
      })
    );
  }

  changeTarget(voyageCargoSnapshotId: string) {
    this.store.dispatch(
      VoyageCargoSnapshotActions.get_Target_Voyage_Cargo_Snapshot({
        voyageCargoSnapshotId,
        path: this.path,
      })
    );
  }

  writeEmptyMessage(path: ComparePath): string {
    const type =
      path === ComparePath.Cargo
        ? 'cargo'
        : path === ComparePath.MaterialDetails
        ? 'material details'
        : 'bulks';
    return `No ${type} submitted with the selected versions.`;
  }
}
