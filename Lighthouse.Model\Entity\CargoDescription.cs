﻿namespace Lighthouse.Model.Entity {
    public class CargoDescription {

        public CargoDescription() {
            this.CargoDescriptionId = Guid.NewGuid();
        }
        public Guid CargoDescriptionId { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid CreatedById { get; set; }
        public User CreatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedById { get; set; }
        public User UpdatedBy { get; set; }
        public bool Deleted { get; set; }
        public bool Disabled { get; set; }
        public bool IsAdhoc { get; set; } = false;
    }
}
