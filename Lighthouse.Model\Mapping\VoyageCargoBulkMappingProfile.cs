﻿namespace Lighthouse.Model.Mapping {
    public class VoyageCargoBulkMappingProfile : Profile {

        public VoyageCargoBulkMappingProfile() {
            CreateMap<VoyageCargoBulk, VoyageCargoBulkModel>()
                .ForMember(d => d.CreatedByName, s => s.MapFrom(p => p.CreatedBy.Deleted ? "Deleted User" : $"{p.CreatedBy.Firstname} {p.CreatedBy.Lastname}"))
                .ForMember(d => d.UpdatedByName, s => s.MapFrom(p => p.UpdatedById == null ? "" : p.UpdatedBy.Deleted ? "Deleted User" : $"{p.UpdatedBy.Firstname} {p.UpdatedBy.Lastname}"))
                .ForMember(d => d.VendorVendorName, s => s.MapFrom(p => p.Vendor != null ? p.Vendor.VendorName : ""))
                .ForMember(d => d.VoyageCargoBulkDangerousGood, s => s.MapFrom(p => p.VoyageCargoBulkDangerousGood));

            CreateMap<VoyageCargoBulkUpsertModel, VoyageCargoBulk>()
                .ForMember(d => d.VoyageCargoBulkId, s => s.Ignore())
                .ForMember(d => d.Status, s => s.Ignore());
        }
    }
}
