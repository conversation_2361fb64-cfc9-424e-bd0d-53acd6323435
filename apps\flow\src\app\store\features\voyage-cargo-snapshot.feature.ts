import { createFeature, createReducer } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { Features } from 'libs/services/src/lib/services/voyages/enums/features.enum';
import { initialVoyageCargoSnapshotState } from '../states/voyage-cargo-snapshot.state';
import { VoyageCargoSnapshotActions } from 'libs/services/src/lib/services/voyages/store/actions/voyage-snapshot.actions';

export const voyageCargoSnapshotReducer = createReducer(
  initialVoyageCargoSnapshotState,
  immerOn(
    VoyageCargoSnapshotActions.get_Voyage_Cargo_Snapshot_List_Success,
    (state, payload) => {
      state.voyageCargoSnapshotList = payload.voyageCargoSnapshotList;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Source_Voyage_Cargo_Snapshot,
    (state, payload) => {
      state.sourceLoading = true;
      state.selectedSourceId = payload.voyageCargoSnapshotId;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Source_Voyage_Cargo_Snapshot_Error,
    (state) => {
      state.sourceLoading = false;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Source_Voyage_Cargo_Snapshot_Success,
    (state, payload) => {
      state.source = payload.source;
      state.sourceLoading = false;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Target_Voyage_Cargo_Snapshot,
    (state, payload) => {
      state.targetLoading = true;
      state.selectedTargetId = payload.voyageCargoSnapshotId;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Target_Voyage_Cargo_Snapshot_Error,
    (state) => {
      state.targetLoading = false;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Target_Voyage_Cargo_Snapshot_Success,
    (state, payload) => {
      state.target = payload.target;
      state.targetLoading = false;
    }
  ),
  immerOn(VoyageCargoSnapshotActions.create_Voyage_Cargo_Snapshot, (state) => {
    state.loading = true;
  }),
  immerOn(
    VoyageCargoSnapshotActions.create_Voyage_Cargo_Snapshot_Success,
    VoyageCargoSnapshotActions.create_Voyage_Cargo_Snapshot_Error,
    (state) => {
      state.loading = false;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Can_Create_New_Version_Success,
    (state, payload) => {
      state.canCreateNewVersion = payload.canCreate;
    }
  ),
  immerOn(
    VoyageCargoSnapshotActions.get_Can_Create_New_Version_Error,
    (state) => {
      state.canCreateNewVersion = false;
    }
  )
);

export const voyageCargoSnapshotFeature = createFeature({
  name: Features.VoyageCargoSnapshot,
  reducer: voyageCargoSnapshotReducer,
});
