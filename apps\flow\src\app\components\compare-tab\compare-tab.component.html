<p-card>
  <div class="d-flex mb-16">
    <p-dropdown
      [options]="voyageCargoSnapshotList()"
      [ngModel]="selectedSourceId() || currentVersion"
      optionLabel="version"
      optionValue="voyageCargoSnapshotId"
      placeholder="Select"
      styleClass="new-version"
      panelStyleClass="new-version-panel"
      [style.min-width.px]="220"
      (onChange)="changeSource($event.value)"
    />
  </div>

  <flow-compare-table
    [cargoList]="sourceCargoList()"
    [listColumns]="listColumns()"
    [emptyMessage]="emptyMessage"
    [loading]="sourceLoading()"
  ></flow-compare-table>
</p-card>

<p-card class="mt-16">
  <div class="d-flex mb-16">
    <p-dropdown
      [options]="voyageCargoSnapshotList()"
      [ngModel]="
        selectedTargetId() || voyageCargoSnapshotList()[0].voyageCargoSnapshotId
      "
      optionLabel="version"
      optionValue="voyageCargoSnapshotId"
      placeholder="Select"
      styleClass="new-version"
      panelStyleClass="new-version-panel"
      [style.min-width.px]="220"
      (onChange)="changeTarget($event.value)"
    />
  </div>

  <flow-compare-table
    [cargoList]="targetCargoList()"
    [listColumns]="listColumns()"
    [emptyMessage]="emptyMessage"
    [loading]="targetLoading()"
  ></flow-compare-table>
</p-card>
