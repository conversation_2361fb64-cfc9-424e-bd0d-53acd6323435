import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

import { VesselTankActions } from '../actions/vessel-tank.actions';
import { VesselTank } from '../../interfaces/vessel-tank.interface';
import { FileService } from '../../../file.service';
import { VesselTankService } from '../../vessel-tank.service';
import { TankType } from '../../../maintenance/interfaces/tank-type.interface';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';

export const loadVesselTanks = createEffect(
  (actions = inject(Actions), service = inject(VesselTankService)) => {
    return actions.pipe(
      ofType(
        VesselTankActions.initialize_Vessel_Tanks,
        VesselTankActions.load_Vessel_Tanks_Lists,
        VesselTankActions.remove_Vessel_Tank_Success,
        VesselTankActions.add_Vessel_Tank_Success,
        VesselTankActions.edit_Vessel_Tank_Success
      ),
      mergeMap(({ vesselId }) =>
        service.loadTankListByVesselId(vesselId).pipe(
          map((vesselTanks) =>
            VesselTankActions.load_Vessel_Tanks_Lists_Success({
              vesselTanks,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Tanks_Lists_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadTankTypes = createEffect(
  (
    actions = inject(Actions),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(
        VesselTankActions.load_Vessel_Tank_Types,
        VesselTankActions.initialize_Vessel_Tanks
      ),
      mergeMap(() =>
        vesselTankService.loadTankTypes().pipe(
          map((tankTypes) =>
            VesselTankActions.load_Vessel_Tank_Types_Success({ tankTypes })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Tank_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkTypes = createEffect(
  (actions = inject(Actions), service = inject(VesselTankService)) => {
    return actions.pipe(
      ofType(
        VesselTankActions.load_Vessel_Bulk_Types,
        VesselTankActions.initialize_Vessel_Tanks
      ),
      mergeMap(() =>
        service.loadBulkTypes().pipe(
          map((bulkTypes) =>
            VesselTankActions.load_Vessel_Bulk_Types_Success({ bulkTypes })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Bulk_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeVesselTank = createEffect(
  (actions = inject(Actions), service = inject(VesselTankService)) => {
    return actions.pipe(
      ofType(VesselTankActions.remove_Vessel_Tank),
      mergeMap(({ vesselTankId, vesselId }) =>
        service.removeTank(vesselTankId).pipe(
          map(() =>
            VesselTankActions.remove_Vessel_Tank_Success({
              vesselId,
              successMessage: 'VesselTank remove successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.remove_Vessel_Tank_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addVesselTank = createEffect(
  (actions = inject(Actions), service = inject(VesselTankService)) => {
    return actions.pipe(
      ofType(VesselTankActions.add_Vessel_Tank),
      mergeMap(({ vesselTank, vesselId }) =>
        service.addTank(vesselTank).pipe(
          map(() =>
            VesselTankActions.add_Vessel_Tank_Success({
              vesselId,
              successMessage: 'Vessel Tank added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.add_Vessel_Tank_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editVesselTank = createEffect(
  (actions = inject(Actions), service = inject(VesselTankService)) => {
    return actions.pipe(
      ofType(VesselTankActions.edit_Vessel_Tank),
      mergeMap(({ vesselTankId, vesselTank, vesselId }) =>
        service.editTank(vesselTankId, vesselTank).pipe(
          map(() =>
            VesselTankActions.edit_Vessel_Tank_Success({
              vesselId,
              successMessage: 'Vessel Tank edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.edit_Vessel_Tank_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportVesselTanks = createEffect(
  (
    actions = inject(Actions),
    service = inject(VesselTankService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.export_Vessel_Tanks),
      mergeMap(({ vesselId }) =>
        service.exportVesselTanks(vesselId).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Vessel Tanks');
            return VesselTankActions.export_Vessel_Tanks_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.export_Vessel_Tanks_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);
