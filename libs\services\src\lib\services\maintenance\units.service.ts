import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Unit } from '../interfaces/unit.interface';
import { HttpClient } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class UnitsService {
  private readonly http = inject(HttpClient);

  loadUnits(): Observable<Unit[]> {
    return this.http.get<Unit[]>('/api/unit');
  }

  removeUnit(id: string): Observable<Unit> {
    return this.http.delete<Unit>(`/api/unit/${id}`);
  }

  addUnit(unit: Unit): Observable<Unit> {
    return this.http.post<Unit>('/api/unit', unit);
  }

  editUnit(id: string, unit: Unit): Observable<Unit> {
    return this.http.put<Unit>(`/api/unit/${id}`, unit);
  }
}
