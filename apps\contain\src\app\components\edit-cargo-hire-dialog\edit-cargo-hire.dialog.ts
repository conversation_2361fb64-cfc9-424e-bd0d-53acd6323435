import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import {
  assetsFeature,
  locationsFeature,
  operatorsFeature,
  vendorsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ConfirmationService } from 'primeng/api';

import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestCargoEdit } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo-edit.interface';
import { Location } from 'libs/services/src/lib/services/maintenance/interfaces/location.interface';
import { RoleCheckService } from 'libs/services/src/lib/services/shared/role-check.service';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'contain-edit-cargo-hire-dialog',
  standalone: true,
  imports: [
    DialogModule,
    TableModule,
    ReactiveFormsModule,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    InputTextModule,
    InputTextareaModule,
  ],
  templateUrl: './edit-cargo-hire.dialog.html',
  styleUrls: ['./edit-cargo-hire.dialog.scss'],
})
export class EditCargoHireDialog implements OnInit {
  @Output() dialogToggle = new EventEmitter<boolean>();
  @Input() dialogVisible: boolean = false;
  @Input() hireRequestCargo!: HireRequestCargo;
  private readonly confirmationService = inject(ConfirmationService);

  roleCheckService = inject(RoleCheckService);
  store = inject(Store);

  assets = this.store.selectSignal(assetsFeature.selectAssets);
  clients = this.store.selectSignal(operatorsFeature.selectOperators);
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  vendors = this.store.selectSignal(vendorsFeature.selectVendorsList);

  userCargoLocations: Location[] = [];
  cargoHireForm: FormGroup = new FormGroup({});
  private returnedDateSignal = signal<Date | null>(null);

  ngOnInit(): void {
    this.cargoHireForm = this.initialiseForm();
    this.cargoHireForm.controls['cargoCCUId'].disable();
    this.cargoHireForm.controls['cargoUnitType'].disable();

    // Subscribe to value changes of the 'returned' form control, needed for the returned date signal and minOffHiredDate
    this.cargoHireForm.get('returned')?.valueChanges.subscribe((value) => {
      this.returnedDateSignal.set(value ? new Date(value) : null);
    });

    const initialValue = this.cargoHireForm.get('returned')?.value;
    this.returnedDateSignal.set(initialValue ? new Date(initialValue) : null);
  }

  checkUserRole(): void {
    const currentUser = this.currentUser();

    switch (true) {
      case this.roleCheckService.checkUserApplicationRole(
        currentUser,
        'allocate',
        'SUP'
      ):
        this.userCargoLocations = this.locations();
        break;
      case this.roleCheckService.checkUserApplicationRole(
        currentUser,
        'allocate',
        'ADM'
      ):
        const userClientId = currentUser?.clientId;

        if (userClientId) {
          const client = this.clients().find(
            (c) => c.clientId === userClientId
          );

          if (client) {
            this.userCargoLocations = this.locations().filter((l) =>
              client.locationIds?.includes(l.locationId)
            );
          }
        }
        break;
      case this.roleCheckService.checkUserApplicationRole(
        currentUser,
        'allocate',
        'USR'
      ):
        const userLocation = this.locations().find(
          (l) => l.locationId === currentUser?.locationId
        );
        if (userLocation) {
          this.userCargoLocations = [userLocation];
        }
        break;
      default:
        const location = this.locations().find(
          (l) => l.locationId === currentUser?.locationId
        );
        if (location) {
          this.userCargoLocations = [location];
        }
        break;
    }
  }

  initialiseForm(): FormGroup {
    return new FormGroup({
      vendorId: new FormControl<string | null>(
        this.hireRequestCargo.vendorId,
        Validators.required
      ),
      cargoCCUId: new FormControl<string | null>(
        this.hireRequestCargo.cargoCCUId,
        Validators.required
      ),
      cargoUnitType: new FormControl<string | null>(
        this.hireRequestCargo.cargoUnitType,
        Validators.required
      ),
      clientId: new FormControl<string | null>(
        this.hireRequestCargo.clientId,
        Validators.required
      ),
      billingAssetId: new FormControl<string | null>(
        this.hireRequestCargo.billingAssetId,
        Validators.required
      ),
      onHiredDate: new FormControl<Date | undefined>(
        this.hireRequestCargo.onHiredDate
          ? new Date(this.hireRequestCargo.onHiredDate)
          : undefined,
        Validators.required
      ),
      vendorOutboundDate: new FormControl<Date | undefined>(
        this.hireRequestCargo.vendorOutboundDate
          ? new Date(this.hireRequestCargo.vendorOutboundDate)
          : undefined
      ),
      shipped: new FormControl<Date | undefined>(
        this.hireRequestCargo.shipped
          ? new Date(this.hireRequestCargo.shipped)
          : undefined
      ),
      returned: new FormControl<Date | undefined>(
        this.hireRequestCargo.returned
          ? new Date(this.hireRequestCargo.returned)
          : undefined
      ),
      vendorInboundDate: new FormControl<Date | undefined>(
        this.hireRequestCargo.vendorInboundDate
          ? new Date(this.hireRequestCargo.vendorInboundDate)
          : undefined
      ),
      offHiredDate: new FormControl<Date | undefined>(
        this.hireRequestCargo.offHiredDate
          ? new Date(this.hireRequestCargo.offHiredDate)
          : undefined
      ),
      reference: new FormControl<string | null>(
        this.hireRequestCargo.reference
      ),
      manifestOut: new FormControl<string | null>(
        this.hireRequestCargo.manifestOut
      ),
      manifestIn: new FormControl<string | null>(
        this.hireRequestCargo.manifestIn
      ),
      assetId: new FormControl<string | null>(this.hireRequestCargo.assetId),
      vendorOutbound: new FormControl<string | null>(
        this.hireRequestCargo.vendorOutbound
      ),
      vendorInbound: new FormControl<string | null>(
        this.hireRequestCargo.vendorInbound
      ),
      consignmentNumber: new FormControl<string | null>(
        this.hireRequestCargo.consignmentNumber
      ),
      containerPoolId: new FormControl<string | null>(
        this.hireRequestCargo.containerPoolId
      ),
      longTermHire: new FormControl<boolean | undefined>(
        this.hireRequestCargo.longTermHire
      ),
      netSupplied: new FormControl<boolean | undefined>(
        this.hireRequestCargo.netSupplied
      ),
      netReturned: new FormControl<boolean | undefined>(
        this.hireRequestCargo.netReturned
      ),
      tarpaulinSupplied: new FormControl<boolean | undefined>(
        this.hireRequestCargo.tarpaulinSupplied
      ),
      tarpaulinReturned: new FormControl<boolean | undefined>(
        this.hireRequestCargo.tarpaulinReturned
      ),
      shelvesSupplied: new FormControl<boolean | undefined>(
        this.hireRequestCargo.shelvesSupplied
      ),
      shelvesReturned: new FormControl<boolean | undefined>(
        this.hireRequestCargo.shelvesReturned
      ),
    });
  }

  submit(): void {
    this.cargoHireForm.markAllAsTouched();

    if (this.cargoHireForm.valid) {
      const model: HireRequestCargoEdit = {
        ...this.cargoHireForm.value,
        onHiredDate: this.cargoHireForm.value.onHiredDate
          ? stripTimezoneOffset(this.cargoHireForm.value.onHiredDate)
          : undefined,
        vendorOutboundDate: this.cargoHireForm.value.vendorOutboundDate
          ? stripTimezoneOffset(this.cargoHireForm.value.vendorOutboundDate)
          : undefined,
        shipped: this.cargoHireForm.value.shipped
          ? stripTimezoneOffset(this.cargoHireForm.value.shipped)
          : undefined,
        returned: this.cargoHireForm.value.returned
          ? stripTimezoneOffset(this.cargoHireForm.value.returned)
          : undefined,
        vendorInboundDate: this.cargoHireForm.value.vendorInboundDate
          ? stripTimezoneOffset(this.cargoHireForm.value.vendorInboundDate)
          : undefined,
        offHiredDate: this.cargoHireForm.value.offHiredDate
          ? stripTimezoneOffset(this.cargoHireForm.value.offHiredDate)
          : undefined,
      };

      this.store.dispatch(
        HireRequestActions.update_Cargo_Hire({
          hireRequestCargoId: this.hireRequestCargo.hireRequestCargoId,
          model: model,
          filterModel: this.filterModel(),
        })
      );

      this.hideDialog();
      this.cargoHireForm = this.initialiseForm();
    }
  }

  hideDialog(isDeleteDialog: boolean = false): void {
    this.dialogToggle.emit(isDeleteDialog);
  }

  removeHire(): void {
    this.store.dispatch(
      HireRequestActions.remove_Hire_Request_Cargo({
        hireRequestCargoId: this.hireRequestCargo.hireRequestCargoId,
        filterModel: this.filterModel(),
      })
    );
    this.hideDialog(true);
  }

  onDelete(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to delete this Hire?',
      header: 'Delete',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      accept: () => {
        this.removeHire();
      },
    });
  }

  minOffHiredDate = computed(() => {
    const returnedDate = this.returnedDateSignal();

    if (returnedDate) {
      return new Date(returnedDate);
    }

    return new Date();
  });
}
