import { inject } from '@angular/core';
import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of, switchMap } from 'rxjs';

import { ComparePageActions } from '../actions/compare-page.action';
import { FlowActions } from '../../../../../../libs/services/src/lib/services/voyages/store/actions/flow.actions';
import { AssetsLocationActions } from 'libs/services/src/lib/services/assets-locations/store/actions/assets-locations.actions';
import { VoyageCargoSnapshotActions } from '../../../../../../libs/services/src/lib/services/voyages/store/actions/voyage-snapshot.actions';
import { voyageCargoSnapshotFeature } from '../features/voyage-cargo-snapshot.feature';
import { VendorsActions } from 'libs/services/src/lib/services/maintenance/store/actions/vendor.actions';
import { WeightCategoriesActions } from 'libs/services/src/lib/services/maintenance/store/actions/weight-category.actions';
import { SiteActions } from 'libs/services/src/lib/services/maintenance/store/actions/site.actions';
import { DangerousGoodActions } from 'libs/services/src/lib/services/maintenance/store/actions/dangerous-good.actions';
import { BulkTypeActions } from 'libs/services/src/lib/services/maintenance/store/actions/bulk-type.actions';

export const initializeComparePage = createEffect(
  (actions$ = inject(Actions)) => {
    return actions$.pipe(
      ofType(ComparePageActions.initialize_Page),
      switchMap(({ voyageId, locationId }) =>
        of(
          FlowActions.load_Voyage_Details({ voyageId }),
          AssetsLocationActions.load_Asset_Locations_By_LocationId({
            locationId,
          }),
          VendorsActions.load_Vendors_By_Location_Id({
            locationId,
          }),
          WeightCategoriesActions.load_WeightCategories_By_Location_Id_And_Voyage_Id(
            { locationId, voyageId }
          ),
          SiteActions.load_Sites_By_Location_Id({ id: locationId }),
          DangerousGoodActions.load_Dangerous_Goods_By_Location_Id({
            locationId,
          }),
          BulkTypeActions.load_Bulk_Types()
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const initializeSourceAndTarget = createEffect(
  (actions = inject(Actions), store = inject(Store)) =>
    actions.pipe(
      ofType(ComparePageActions.initialize_Tab),

      // Get latest version ID: snapshot list, selected source/target IDs
      concatLatestFrom(() => [
        store.select(voyageCargoSnapshotFeature.selectVoyageCargoSnapshotList),
        store.select(voyageCargoSnapshotFeature.selectSelectedSourceId),
        store.select(voyageCargoSnapshotFeature.selectSelectedTargetId),
      ]),

      switchMap(
        ([
          { version, path },
          snapshotList,
          sourceIdFromState,
          targetIdFromState,
        ]) => {
          const firstSnapshotId = snapshotList[0]?.voyageCargoSnapshotId;
          const sourceId = sourceIdFromState || version || firstSnapshotId;
          const targetId = targetIdFromState || firstSnapshotId;

          return of(
            VoyageCargoSnapshotActions.get_Source_Voyage_Cargo_Snapshot({
              voyageCargoSnapshotId: sourceId,
              path,
            }),
            VoyageCargoSnapshotActions.get_Target_Voyage_Cargo_Snapshot({
              voyageCargoSnapshotId: targetId,
              path,
            })
          );
        }
      )
    ),
  { functional: true }
);
