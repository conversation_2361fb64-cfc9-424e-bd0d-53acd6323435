import { MaintenanceState } from './maintenance-state.interface';
import { Cargo } from './cargoes.interface';
import { CargoUnitType } from './cargo-unit-type.interface';
import { OneOffCargo } from '../../transport-requests/interfaces/one-off-cargo-interface';

export interface CargoesState extends MaintenanceState {
  cargoes: Cargo[];
  cargo: Cargo;
  locationCargoes: Cargo[];
  voyageId: string;
  cargoDescriptions: CargoUnitType[];
  oneOffCargo: OneOffCargo | null;
  cargoesIncludingAdhoc: Cargo[];
}
