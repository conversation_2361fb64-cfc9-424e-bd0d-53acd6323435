import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';

@Component({
  selector: 'lha-vessel-header',
  standalone: true,
  imports: [HeaderNavComponent],
  templateUrl: './vessel-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VesselHeaderComponent {
  private readonly store = inject(Store);
  vessel = this.store.selectSignal(vesselsFeature.selectVessel);
  categoriesItems = [
    {
      title: 'Details',
      link: 'details',
      permissions: [],
    },
    {
      title: 'Tanks',
      link: 'tanks',
      permissions: [],
    },
    {
      title: 'Hire Statement',
      link: 'hire-statement',
      permissions: [],
    },
  ];
}
