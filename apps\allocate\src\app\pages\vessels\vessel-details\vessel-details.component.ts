import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Store } from '@ngrx/store';

import { VesselHeaderComponent } from '../../../components/vessels/vessel-header/vessel-header.component';
import { VesselActions } from 'libs/services/src/lib/services/vessels/store/actions/vessels.actions';

@Component({
  selector: 'lha-vessel-details',
  standalone: true,
  imports: [RouterOutlet, VesselHeaderComponent],
  templateUrl: './vessel-details.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VesselDetailsComponent implements OnInit {
  @Input() vesselId = '';
  private readonly store = inject(Store);

  ngOnInit(): void {
    this.store.dispatch(
      VesselActions.set_VesselId({ vesselId: this.vesselId })
    );
  }
}
