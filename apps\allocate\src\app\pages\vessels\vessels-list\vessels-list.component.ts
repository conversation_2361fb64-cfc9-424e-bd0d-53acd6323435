import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';
import { ConfirmationService } from 'primeng/api';

import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { VesselActions } from 'libs/services/src/lib/services/vessels/store/actions/vessels.actions';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';
import { InitializeVesselsTable } from '../../../shared/tables/vessels.table';
import { VesselsTableFields } from '../../../shared/enums/vessels-table-fields.enum';
import { HasPermissionDirective } from '../../../shared/directives/hasPermissions.directive';

@Component({
  selector: 'lha-vessels-list',
  standalone: true,
  imports: [
    DatePipe,
    RouterLink,
    HasPermissionDirective,
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    NgIf,
  ],
  templateUrl: './vessels-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VesselsListComponent implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  loading = this.store.selectSignal(vesselsFeature.selectLoading);
  searchValue = '';
  listColumns = InitializeVesselsTable();
  vessels: Vessel[] = [];
  tableFields = VesselsTableFields;
  userRole = UserRole;
  tableWidth = 850;

  ngOnInit(): void {
    this.store.dispatch(VesselActions.load_Vessels());

    this.actions
      .pipe(
        ofType(VesselActions.load_Vessels_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ vessels }) => {
        this.searchValue = '';
        this.vessels = [...vessels];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  loadVessels(): void {
    this.store.dispatch(VesselActions.load_Vessels());
  }

  remove(vessel: Vessel): void {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
        Deleting this Vessel may affect other parts of Allocate.
        <br>
        Do you want to remove this Vessel?
      `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          VesselActions.remove_Vessel({
            id: vessel.vesselId,
          })
        );
      },
    });
  }
}
