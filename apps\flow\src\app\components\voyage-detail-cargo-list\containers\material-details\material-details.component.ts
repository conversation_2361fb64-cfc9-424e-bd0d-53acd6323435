import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnChanges,
  OnInit,
  computed,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { MaterialDetailTableFields } from 'apps/flow/src/app/shared/enums';
import { Lookups } from 'apps/flow/src/app/shared/helpers/tables/lookups';
import { FilterMaterialDetailList } from 'apps/flow/src/app/shared/interfaces';
import { MaterialDetailActions } from 'apps/flow/src/app/store/actions/material-details.action';
import { bulkListEditFeature } from 'apps/flow/src/app/store/features/bulk-list-edit.feature';
import { materialDetailFeature } from 'apps/flow/src/app/store/features/material-details.feature';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { FieldType } from 'libs/components/src/lib/enums';
import { assetsLocationsFeature } from 'libs/services/src/lib/services/assets-locations/store/features';
import {
  dangerousGoodsFeature,
  vendorsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { CountriesList } from 'libs/services/src/lib/services/shared/countries-list';
import { CurrencyList } from 'libs/services/src/lib/services/shared/currency-list';
import { RemoveSecondsPipe } from 'libs/components/src/lib/pipes';

import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { cargoListEditPageFeature } from 'libs/services/src/lib/services/voyages/store/features/cargo-list-edit-page.feature';
import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';

@Component({
  standalone: true,
  selector: 'app-material-details',
  templateUrl: './material-details.component.html',
  styleUrls: ['./material-details.component.css'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    CheckboxModule,
    DropdownModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    InputTextModule,
    InputNumberModule,
    RemoveSecondsPipe,
    NgClass,
    NgIf
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MaterialDetailsComponent implements OnInit {
  store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  assets = this.store.selectSignal(assetsLocationsFeature.selectAssets);
  vendors = this.store.selectSignal(vendorsFeature.selectVoyageVendorsList);
  voyageData = this.store.selectSignal(flowFeature.selectVoyage);
  bulkList = this.store.selectSignal(bulkListEditFeature.selectBulkList);
  cargoList = this.store.selectSignal(cargoListEditPageFeature.selectCargoList);
  dangerousGoods = this.store.selectSignal(
    dangerousGoodsFeature.selectDangerousGoodsByLocationId
  );
  listColumns = this.store.selectSignal(
    materialDetailFeature.selectMaterialDetailTableViewModeListColumns
  );
  filteredMaterialDetailList = this.store.selectSignal(
    materialDetailFeature.filteredMaterialDetailList
  );
  tableWidth = this.store.selectSignal(
    materialDetailFeature.selectMaterialDetailTableWidth
  );
  tableLoading = this.store.selectSignal(materialDetailFeature.selectIsLoading);

  assetsForFilter = computed(() => [
    {
      label: 'All',
      value: 'All',
    },
    ...this.mappedAssets(),
  ]);
  mappedAssets = computed(() => {
    return this.assets().map((data) => ({
      label: data.name,
      value: data.assetId,
    }));
  });
  dropdownLookups = computed<{
    [key: string]: {
      label: string;
      value: any;
    }[];
  }>(() => {
    return {
      ...Lookups,
      vendorId: this.vendors()?.map((item) => ({
        label: item.vendorName,
        value: item.vendorId,
      })),
      assetId: this.assets()?.map((data) => ({
        label: data.name,
        value: data.assetId,
      })),
      voyageCargoBulkId: this.bulkList()?.map((data) => ({
        label: data.rowNumber.toString(),
        value: data.voyageCargoBulkId,
      })),
      voyageCargoId: this.cargoList()?.map((data) => ({
        label: data.rowNumber.toString(),
        value: data.voyageCargoId,
      })),
      dangerousGoodId: this.dangerousGoods()?.map((data) => ({
        label: data.unNo,
        value: data.dangerousGoodId,
      })),
      coo: CountriesList.map((country) => ({
        label: country.name,
        value: country.code,
      })),
      currency: CurrencyList.map((country) => ({
        label: country.code,
        value: country.code,
      })),
    };
  });

  filterForm = new FormGroup({
    assets: new FormControl('All'),
    isCancelled: new FormControl(false),
  });

  fieldType = FieldType;
  materialDetailTableFields = MaterialDetailTableFields;

  ngOnInit() {
    this.store.dispatch(
      MaterialDetailActions.initialize_Material_Detail_List({
        locationId: this.currentUser().locationId!,
        voyageId: this.voyageData()!.voyageId,
      })
    );

    this.filterForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.store.dispatch(
          MaterialDetailActions.filter_Material_Detail_List({
            filter: value as FilterMaterialDetailList,
          })
        );
      });
  }
}
