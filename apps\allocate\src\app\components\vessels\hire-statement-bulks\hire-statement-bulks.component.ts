import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import {
  DatePipe,
  DecimalPipe,
  <PERSON>For,
  <PERSON><PERSON><PERSON>,
  NgSwitch,
  Ng<PERSON><PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { Table, TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ConfirmationService } from 'primeng/api';

import { HireStatementBulk } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement-bulk.interface';
import { HireStatementBulkActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement-bulk.actions';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features/hire-statements.feature';
import { InitializeHireStatementBulkTable } from '../../../shared/tables/hire-statement-bulk.table';
import { HireStatementBulkTableFields } from '../../../shared/enums/hire-statement-bulk-table.enum';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features/setting.features';
import { HireStatementBulkAddEditComponent } from '../hire-statement-bulk-add-edit/hire-statement-bulk-add-edit.component';

@Component({
  standalone: true,
  selector: 'lha-hire-statement-bulks',
  templateUrl: './hire-statement-bulks.component.html',
  imports: [
    FormsModule,
    InputTextModule,
    DecimalPipe,
    TableModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgFor,
    DatePipe,
    NgIf,
    HireStatementBulkAddEditComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HireStatementBulksComponent implements OnInit {
  @ViewChild('table') table!: Table;
  @Input() set hireStatementId(value: string) {
    this.#hireStatementId.set(value);
  }

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  loading = this.store.selectSignal(hireStatementFeature.selectLoading);
  hireStatements = this.store.selectSignal(
    hireStatementFeature.selectHireStatements
  );
  vesselId = this.store.selectSignal(vesselsFeature.selectVesselId);
  searchValue = '';
  listColumns = InitializeHireStatementBulkTable();
  hireStatementBulks: HireStatementBulk[] = [];
  tableFields = HireStatementBulkTableFields;
  tableWidth = 850;
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);
  #hireStatementId = signal('');
  isOffHire = computed(() => {
    return !this.hireStatements().find(
      (item) => item.hireStatementId === this.#hireStatementId()
    )?.isOnHire;
  });

  constructor() {
    effect(
      () => {
        if (this.vesselId() && this.#hireStatementId()) {
          this.store.dispatch(
            HireStatementBulkActions.load_Hire_Statement_Bulks({
              hireStatementId: this.#hireStatementId(),
            })
          );
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit() {
    this.actions
      .pipe(
        ofType(HireStatementBulkActions.load_Hire_Statement_Bulks_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ hireStatementBulks }) => {
        this.searchValue = '';
        this.hireStatementBulks = [...hireStatementBulks];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export(): void {
    this.store.dispatch(
      HireStatementBulkActions.export_Hire_Statement_Bulks({
        hireStatementId: this.#hireStatementId(),
      })
    );
  }

  add(): void {
    this.store.dispatch(
      HireStatementBulkActions.change_visibility_add_edit({
        visible: true,
        hireStatementBulk: null,
      })
    );
  }

  edit(hireStatementBulk: HireStatementBulk): void {
    this.store.dispatch(
      HireStatementBulkActions.change_visibility_add_edit({
        visible: true,
        hireStatementBulk,
      })
    );
  }

  remove(hireStatementBulk: HireStatementBulk): void {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
            Deleting this Hire Statement Bulk may affect other parts of Allocate.
            <br>
            Do you want to remove this Hire Statement Bulk?
          `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          HireStatementBulkActions.remove_Hire_Statement_Bulk({
            hireStatementId: hireStatementBulk.hireStatementId,
            hireStatementBulkId: hireStatementBulk.hireStatementBulkId,
          })
        );
      },
    });
  }
}
