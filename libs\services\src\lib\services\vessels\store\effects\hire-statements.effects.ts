import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of, switchMap } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { HireStatementActions } from '../actions/hire-statement.actions';
import { HireStatement } from '../../interfaces/hire-statement.interface';
import { FileService } from '../../../file.service';
import { HireStatementService } from '../../hire-statement.service';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HireStatementBulkActions } from '../actions/hire-statement-bulk.actions';
import { HireStatementBulk } from '../../interfaces/hire-statement-bulk.interface';
import { BulkTypeActions } from '../../../maintenance/store/actions/bulk-type.actions';

export const loadHireStatements = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(
        HireStatementActions.load_Hire_Statements,
        HireStatementActions.remove_Hire_Statement_Success,
        HireStatementActions.add_Hire_Statement_Success,
        HireStatementActions.edit_Hire_Statement_Success
      ),

      mergeMap(({ vesselId }) =>
        service.loadHireStatementListByVesselId(vesselId).pipe(
          map((hireStatements) =>
            HireStatementActions.load_Hire_Statements_Success({
              hireStatements,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.load_Hire_Statements_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const load_Bulk_Types = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(HireStatementActions.load_Hire_Statements),
      switchMap(() => of(BulkTypeActions.load_Bulk_Types()))
    );
  },
  { functional: true }
);

export const removeHireStatement = createEffect(
  (
    actions = inject(Actions),
    service = inject(HireStatementService),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.remove_Hire_Statement),
      mergeMap(({ hireStatementId, vesselId }) =>
        service.removeHireStatement(hireStatementId).pipe(
          map(() => {
            router.navigate(['vessels', vesselId, 'hire-statement']);
            return HireStatementActions.remove_Hire_Statement_Success({
              vesselId,
              successMessage: 'Hire Statement removed successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.remove_Hire_Statement_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addHireStatement = createEffect(
  (
    actions = inject(Actions),
    service = inject(HireStatementService),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.add_Hire_Statement),
      mergeMap(({ hireStatement, vesselId }) =>
        service.addHireStatement(hireStatement).pipe(
          map(() => {
            router.navigate(['vessels', vesselId, 'hire-statement']);
            return HireStatementActions.add_Hire_Statement_Success({
              vesselId,
              successMessage: 'Hire statement added successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementActions.add_Hire_Statement_Failure({ error: error })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editHireStatement = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(HireStatementActions.edit_Hire_Statement),
      mergeMap(({ hireStatement, vesselId, hireStatementId }) =>
        service.editHireStatement(hireStatementId, hireStatement).pipe(
          map((res: HireStatement) =>
            HireStatementActions.edit_Hire_Statement_Success({
              vesselId,
              successMessage: 'Hire Statement edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementActions.edit_Hire_Statement_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportHireStatements = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.export_Hire_Statements),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        hireStatementService.exportHireStatements(params['vesselId']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Hire Statements');
            return HireStatementActions.export_Hire_Statements_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.export_Hire_Statements_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const loadHireStatementBulk = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(
        HireStatementBulkActions.load_Hire_Statement_Bulks,
        HireStatementBulkActions.remove_Hire_Statement_Bulk_Success,
        HireStatementBulkActions.add_Hire_Statement_Bulk_Success,
        HireStatementBulkActions.edit_Hire_Statement_Bulk_Success
      ),
      mergeMap(({ hireStatementId }) =>
        service.loadHireStatementBulkList(hireStatementId).pipe(
          map((res: HireStatementBulk[]) =>
            HireStatementBulkActions.load_Hire_Statement_Bulks_Success({
              hireStatementBulks: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.load_Hire_Statement_Bulks_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeHireStatementBulk = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.remove_Hire_Statement_Bulk),
      mergeMap(({ hireStatementBulkId, hireStatementId }) =>
        service.removeHireStatementBulk(hireStatementBulkId).pipe(
          map(() =>
            HireStatementBulkActions.remove_Hire_Statement_Bulk_Success({
              hireStatementId,
              successMessage: 'Hire Statement Bulk removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.remove_Hire_Statement_Bulk_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addHireStatementBulk = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.add_Hire_Statement_Bulk),
      mergeMap(({ hireStatementBulk, hireStatementId }) =>
        service.addHireStatementBulk(hireStatementBulk).pipe(
          map(() =>
            HireStatementBulkActions.add_Hire_Statement_Bulk_Success({
              hireStatementId,
              successMessage: 'Hire statement Bulk added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.add_Hire_Statement_Bulk_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editHireStatementBulk = createEffect(
  (actions = inject(Actions), service = inject(HireStatementService)) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.edit_Hire_Statement_Bulk),
      mergeMap(({ hireStatementBulkId, hireStatementBulk, hireStatementId }) =>
        service
          .editHireStatementBulk(hireStatementBulkId, hireStatementBulk)
          .pipe(
            map(() =>
              HireStatementBulkActions.edit_Hire_Statement_Bulk_Success({
                hireStatementId,
                successMessage: 'Hire Statement Bulk edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireStatementBulkActions.edit_Hire_Statement_Bulk_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportHireStatementBulks = createEffect(
  (
    actions = inject(Actions),
    service = inject(HireStatementService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.export_Hire_Statement_Bulks),
      mergeMap(({ hireStatementId }) =>
        service.exportHireStatementBulks(hireStatementId).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Hire Statement Bulks');
            return HireStatementBulkActions.export_Hire_Statement_Bulks_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.export_Hire_Statement_Bulks_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);
