import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  DatePipe,
  NgIf,
  NgClass,
  DecimalPipe,
  NgS<PERSON>,
  NgSwitchCase,
  NgSwitchDefault,
  Ng<PERSON>or,
} from '@angular/common';
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet,
} from '@angular/router';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { Table, TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ConfirmationService } from 'primeng/api';
import { RadioButtonModule } from 'primeng/radiobutton';

import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { HireStatement } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement.interface';
import { HireStatementActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement.actions';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';
import { InitializeHireStatementTable } from '../../../shared/tables/hire-statement.table';
import { HireStatementTableFields } from '../../../shared/enums/hire-statement-table-fields.enum';
import { HireStatementAddEditComponent } from '../hire-statement-add-edit/hire-statement-add-edit.component';

@Component({
  selector: 'lha-hire-statements',
  standalone: true,
  imports: [
    NgIf,
    DatePipe,
    NgClass,
    RouterLink,
    RouterLinkActive,
    DecimalPipe,
    FormsModule,
    InputTextModule,
    RouterOutlet,
    TableModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgFor,
    RadioButtonModule,
    HireStatementAddEditComponent
  ],
  templateUrl: './hire-statements.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HireStatementsComponent implements OnInit {
  @ViewChild('table') table!: Table;

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);

  loading = this.store.selectSignal(hireStatementFeature.selectLoading);
  vesselId = this.store.selectSignal(vesselsFeature.selectVesselId);
  searchValue = '';
  listColumns = InitializeHireStatementTable();
  hireStatements: HireStatement[] = [];
  tableFields = HireStatementTableFields;
  tableWidth = 850;
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);

  ngOnInit(): void {
    this.store.dispatch(
      HireStatementActions.load_Hire_Statements({ vesselId: this.vesselId() })
    );

    this.actions
      .pipe(
        ofType(HireStatementActions.load_Hire_Statements_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ hireStatements }) => {
        this.searchValue = '';
        this.hireStatements = [...hireStatements];
        this.table.reset();
        this.cdr.markForCheck();

        if (
          hireStatements.length &&
          !this.activatedRoute.snapshot.children[0]?.params['hireStatementId']
        ) {
          this.router.navigate([hireStatements[0].hireStatementId], {
            relativeTo: this.activatedRoute,
          });
        }
      });
  }

  exportHireStatements(): void {
    this.store.dispatch(
      HireStatementActions.export_Hire_Statements({ vesselId: this.vesselId() })
    );
  }

  add(): void {
    this.store.dispatch(
      HireStatementActions.change_visibility_add_edit({
        visible: true,
        hireStatement: null,
      })
    );
  }

  edit(hireStatement: HireStatement): void {
    this.store.dispatch(
      HireStatementActions.change_visibility_add_edit({
        visible: true,
        hireStatement,
      })
    );
  }

  remove(item: HireStatement): void {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
        Deleting this Hire Statement may affect other parts of Allocate.
        <br>
        Do you want to remove this Hire Statement?
      `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          HireStatementActions.remove_Hire_Statement({
            hireStatementId: item.hireStatementId,
            vesselId: this.vesselId(),
          })
        );
      },
    });
  }
}
