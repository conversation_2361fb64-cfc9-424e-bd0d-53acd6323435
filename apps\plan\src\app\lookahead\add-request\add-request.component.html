<p-sidebar
  [visible]="isVisibleSchedulePanel()"
  (visibleChange)="closeSideBar($event)"
  position="right"
  [style]="{ width: '400px' }"
>
  <ng-template pTemplate="header">
    <div class="fs-20 f-bold">{{ editMode ? 'Edit' : 'Add' }} Request</div>
  </ng-template>
  <div *ngIf="lookaheadState()">
    <div class="fs-14 fw-400">
      <div class="mb-12">
        <p>Complete the below section for your request sailing</p>
      </div>
      <p-tabView
        styleClass=" border-bottom pb-22"
        [(activeIndex)]="selectedTabIndex"
        [scrollable]="true"
      >
        <p-tabPanel header="Vessel info" class="mb-24">
          <div class="form-section">
            <form [formGroup]="form">
              <div class="w-100">
                <div class="d-flex pt-10 align-content-center">
                  <img
                    src="assets/arrow.svg"
                    alt="users-plus"
                    style="margin-right: 4px; height: 20px; width: 20px"
                  />
                  <b>{{ headerText }}</b>
                </div>
                <p class="sub-header mb-24">
                  Select mandatory fields to create a request.
                </p>
                <div class="mb-12 p-3">
                  <b class="fs-14 fw-bold"
                    >Clients <span style="color: red">*</span></b
                  >
                  <p-dropdown
                    [options]="originalClients()"
                    formControlName="clientId"
                    [filter]="true"
                    optionLabel="clientName"
                    optionValue="clientId"
                    [showClear]="true"
                    placeholder="Select client"
                    styleClass="new-version"
                    panelStyleClass="new-version-panel"
                  >
                  </p-dropdown>
                  <small
                    class="validation-control-error"
                    *ngIf="
                      form.get('clientId')?.invalid &&
                      form.get('clientId')?.touched
                    "
                  >
                    Client selection is required
                  </small>
                </div>

                <div class="mb-12 p-3">
                  <b class="fs-14 fw-bold"
                    >Vessels <span style="color: red">*</span></b
                  >
                  <p-dropdown
                    [options]="vessels()"
                    formControlName="vesselId"
                    [filter]="true"
                    optionLabel="name"
                    optionValue="vesselId"
                    [showClear]="true"
                    appendTo="body"
                    placeholder="Select vessel"
                    styleClass="new-version"
                    (onChange)="updateInboundOutbound()"
                    panelStyleClass="new-version-panel"
                  >
                  </p-dropdown>
                  <small
                    class="validation-control-error"
                    *ngIf="isVesselControlInvalid()"
                  >
                    Vessel selection is required
                  </small>
                </div>

                <div class="mb-12 p-3">
                  <b class="fs-14 fw-bold">Assets</b>
                  <p-multiSelect
                    #multiSelectAssets
                    [options]="originalAssets()"
                    formControlName="sailingRequestAssets"
                    optionValue="assetId"
                    optionLabel="name"
                    appendTo="body"
                    placeholder="Select assets"
                    [showToggleAll]="!multiSelectAssets.isEmpty()"
                    display="comma"
                    [showClear]="
                      !!form.controls.sailingRequestAssets.value?.length
                    "
                    styleClass="new-version-multiselect"
                    panelStyleClass="new-version-panel"
                  ></p-multiSelect>
                </div>

                <div class="date-time-container">
                  <div class="row header-container">
                    <div class="header">
                      <img
                        src="assets/calendar.svg"
                        alt="users-plus"
                        class="mr-8"
                      />
                      <span>Select date and time</span>
                    </div>
                  </div>
                  <div
                    class="row d-flex justify-content-between align-items-center mb-8 mt-8"
                  >
                    <label>Start<span class="required">*</span></label>
                    <p-calendar
                      [minDate]="minStartDate"
                      [maxDate]="timeAfter!"
                      formControlName="startTime"
                      (onSelect)="changeStartDate($event)"
                      dateFormat="dd/mm/yy"
                      placeholder="DD/MM/YYYY"
                      appendTo="body"
                      [showIcon]="true"
                      [readonlyInput]="false"
                      [style]="{ width: '100%' }"
                    >
                    </p-calendar>
                    <small
                      class="p-error"
                      *ngIf="
                        form.controls.startTime.hasError('required') &&
                        form.controls.startTime.touched
                      "
                    >
                      Start date is required
                    </small>
                  </div>
                  <div
                    class="row d-flex justify-content-between align-items-center mb-8"
                  >
                    <label>End <span class="required">*</span></label>
                    <p-calendar
                      [minDate]="timeBefore || today"
                      formControlName="endTime"
                      (onSelect)="changeEndDate($event)"
                      dateFormat="dd/mm/yy"
                      placeholder="DD/MM/YYYY"
                      appendTo="body"
                      [showIcon]="true"
                      [readonlyInput]="false"
                      [style]="{ width: '100%' }"
                    >
                    </p-calendar>
                    <small
                      class="p-error"
                      *ngIf="
                        form.controls.endTime.hasError('required') &&
                        form.controls.endTime.touched
                      "
                    >
                      End date is required
                    </small>
                  </div>
                  <div
                    class="row d-flex justify-content-between align-items-center mb-8"
                  >
                    <div class="input-container">
                      <label>
                        ETA
                        <img
                          [pTooltip]="'Estimated time of Arrival'"
                          src="assets/info.svg"
                          alt="users-plus"
                        />
                      </label>
                      <p-calendar
                        [timeOnly]="true"
                        [showIcon]="true"
                        formControlName="eta"
                        placeholder="HH:MM"
                        [readonlyInput]="false"
                        [style]="{ width: '100%' }"
                      >
                      </p-calendar>
                    </div>
                  </div>
                  <div
                    class="row d-flex justify-content-between align-items-center mb-8"
                  >
                    <div class="input-container">
                      <label>
                        ETD
                        <img
                          [pTooltip]="'Estimated time of Departure'"
                          src="assets/info.svg"
                          alt="users-plus"
                        />
                      </label>
                      <p-calendar
                        [timeOnly]="true"
                        [showIcon]="true"
                        formControlName="etd"
                        [readonlyInput]="false"
                        placeholder="HH:MM"
                        [style]="{ width: '100%' }"
                      >
                      </p-calendar>
                    </div>
                  </div>
                  <div
                    *ngIf="!inboundOutboundVoyageSelected()"
                    class="row recurrence-row"
                  >
                    <div class="input-container">
                      <p-dropdown
                        [ngClass]="
                          form.controls.doesRepeat.value === 'DoesRepeat'
                            ? 'select'
                            : ''
                        "
                        formControlName="doesRepeat"
                        [options]="repeatOptions"
                        placeholder="Does Not Repeat"
                        (onChange)="setDoesRepeat($event.value)"
                        styleClass="new-version"
                        appendTo="body"
                        optionLabel="label"
                        optionValue="value"
                      >
                      </p-dropdown>

                      <button
                        pButton
                        class="btn-tertiary"
                        *ngIf="form.controls.doesRepeat.value === 'DoesRepeat'"
                        (click)="openRepeatDialog()"
                      >
                        <i class="pi pi-pencil" style="color: #a80303"></i>
                      </button>
                    </div>
                  </div>
                  <small
                    class="validation-control-error"
                    *ngIf="inboundOutboundVoyageSelected()"
                  >
                    Cannot create series when voyage is assigned
                  </small>
                </div>
              </div>
              <div class="button-container">
                <div class="row">
                  <label class="select-voyage">Select Voyage</label>
                </div>
                <div class="row add-ons">
                  <div class="col addon-btn">
                    <div
                      class="image-container"
                      [ngClass]="{
                        disabled: vesselNotSelectedOrSeriesSelected(),
                        active: inboundVoyageActive,
                        hover: inboundVoyageHover
                      }"
                      (click)="openInboundOutboundSidebar('Inbound')"
                      (mouseenter)="inboundVoyageHover = true"
                      (mouseleave)="inboundVoyageHover = false"
                    >
                      <img
                        *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
                        src="assets/inbound.svg"
                        class="unchecked"
                      />
                      <img
                        *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
                        src="assets/inbound-hover.svg"
                        class="hover-image"
                      />
                      <img
                        *ngIf="inboundVoyageSelected && inboundVoyageActive"
                        src="assets/inbound-selected-active.svg"
                      />
                      <img
                        *ngIf="inboundVoyageActive && !inboundVoyageSelected"
                        src="assets/inbound-hover.svg"
                      />
                      <img
                        *ngIf="!inboundVoyageActive && inboundVoyageSelected"
                        src="assets/inbound-selected.svg"
                      />
                    </div>
                  </div>
                  <div class="col addon-btn">
                    <div
                      class="image-container"
                      [ngClass]="{
                        disabled: vesselNotSelectedOrSeriesSelected(),
                        active: outboundVoyageActive,
                        hover: outboundVoyageHover
                      }"
                      (click)="openInboundOutboundSidebar('Outbound')"
                      (mouseenter)="outboundVoyageHover = true"
                      (mouseleave)="outboundVoyageHover = false"
                    >
                      <img
                        *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
                        src="assets/outbound.svg"
                        class="unchecked"
                      />
                      <img
                        *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
                        src="assets/outbound-hover.svg"
                        class="hover-image"
                      />
                      <img
                        *ngIf="outboundVoyageSelected && outboundVoyageActive"
                        src="assets/outbound-selected-active.svg"
                      />
                      <img
                        *ngIf="outboundVoyageActive && !outboundVoyageSelected"
                        src="assets/outbound-hover.svg"
                      />
                      <img
                        *ngIf="!outboundVoyageActive && outboundVoyageSelected"
                        src="assets/outbound-selected.svg"
                      />
                    </div>
                  </div>
                </div>
                <small
                  class="validation-control-error"
                  *ngIf="cannotAssignVoyage()"
                >
                  Cannot assign voyage to series
                </small>
              </div>
            </form>
          </div>
        </p-tabPanel>

        <p-tabPanel [disabled]="form.invalid" header="Requirement">
          <div class="form-section">
            <requirement
              [activityCategories]="filteredActivities"
              [units]="units()"
              (activitySelected)="
                handleSailingRequestActivitiesSelected($event)
              "
            ></requirement>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <button (click)="closeSideBar(false)" type="button" class="btn-tertiary">
      Cancel
    </button>
    <button
      class="btn-negative-primary"
      *ngIf="editMode"
      (click)="deleteSailingRequest()"
      type="button"
    >
      Delete
    </button>
    <div *ngIf="selectedTabIndex === 0" class="next-button">
      <button pButton class="btn-primary" (click)="goToNextTab()">Next</button>
    </div>
    <div *ngIf="selectedTabIndex === 1">
      <button
        [disabled]="lookaheadState().loading.createEdit"
        class="btn-primary"
        (click)="save()"
        type="button"
      >
        <span
          [ngStyle]="{
            opacity: lookaheadState().loading.createEdit ? '0' : '1'
          }"
        >
          Save
        </span>
        <p-progressSpinner
          [styleClass]="'small-spinner-style-btn-white'"
          *ngIf="lookaheadState().loading.createEdit"
        ></p-progressSpinner>
      </button>
    </div>
  </ng-template>
</p-sidebar>
