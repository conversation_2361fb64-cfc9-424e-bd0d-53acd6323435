import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { HireStatementActions } from '../actions/hire-statement.actions';
import { hireStatementInitialState } from '../state/hire-statement.state';
import { HireStatementBulkActions } from '../actions/hire-statement-bulk.actions';

export const hireStatementReducer = createReducer(
  hireStatementInitialState,
  immerOn(
    HireStatementActions.remove_Hire_Statement,
    HireStatementActions.add_Hire_Statement_Success,
    HireStatementActions.edit_Hire_Statement_Success,
    HireStatementActions.load_Hire_Statements,
    (state) => {
      state.loading.list = true;
    }
  ),
  immerOn(
    HireStatementActions.load_Hire_Statements_Success,
    (state, { hireStatements }) => {
      state.hireStatements = hireStatements;
      state.loading.list = false;
    }
  ),
  immerOn(
    HireStatementActions.load_Hire_Statements_Failure,
    HireStatementActions.remove_Hire_Statement_Success,
    HireStatementActions.remove_Hire_Statement_Failure,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    HireStatementActions.add_Hire_Statement,
    HireStatementActions.edit_Hire_Statement,
    (state) => {
      state.loading.createEdit = true;
    }
  ),
  immerOn(
    HireStatementActions.add_Hire_Statement_Success,
    HireStatementActions.edit_Hire_Statement_Success,
    (state) => {
      state.isVisibleAddEditHireStatement = false;
      state.hireStatement = null;
    }
  ),
  immerOn(
    HireStatementActions.add_Hire_Statement_Success,
    HireStatementActions.add_Hire_Statement_Failure,
    HireStatementActions.edit_Hire_Statement_Success,
    HireStatementActions.edit_Hire_Statement_Failure,
    (state) => {
      state.loading.createEdit = false;
    }
  ),
  immerOn(HireStatementActions.change_visibility_add_edit, (state, payload) => {
    state.isVisibleAddEditHireStatement = payload.visible;
    state.hireStatement = payload.hireStatement;
  }),
  immerOn(HireStatementActions.export_Hire_Statements, (state) => {
    state.loading.export = true;
  }),
  immerOn(
    HireStatementActions.export_Hire_Statements_Success,
    HireStatementActions.export_Hire_Statements_Failure,
    (state) => {
      state.loading.export = false;
    }
  ),
  immerOn(HireStatementBulkActions.load_Hire_Statement_Bulks, (state) => {
    state.loading.bulkList = true;
  }),
  immerOn(
    HireStatementBulkActions.load_Hire_Statement_Bulks_Success,
    (state, { hireStatementBulks }) => {
      state.hireStatementBulks = hireStatementBulks;
      state.loading.bulkList = false;
    }
  ),
  immerOn(
    HireStatementBulkActions.load_Hire_Statement_Bulks_Failure,
    (state) => {
      state.loading.bulkList = false;
    }
  ),
  immerOn(
    HireStatementBulkActions.add_Hire_Statement_Bulk,
    HireStatementBulkActions.edit_Hire_Statement_Bulk,
    (state) => {
      state.loading.createEdit = true;
    }
  ),

  immerOn(
    HireStatementBulkActions.add_Hire_Statement_Bulk_Success,
    HireStatementBulkActions.add_Hire_Statement_Bulk_Failure,
    HireStatementBulkActions.edit_Hire_Statement_Bulk_Success,
    HireStatementBulkActions.edit_Hire_Statement_Bulk_Failure,
    (state) => {
      state.loading.createEdit = false;
    }
  ),
  immerOn(
    HireStatementBulkActions.change_visibility_add_edit,
    (state, payload) => {
      state.isVisibleAddEditHireStatementBulk = payload.visible;
      state.hireStatementBulk = payload.hireStatementBulk;
    }
  ),
  immerOn(
    HireStatementBulkActions.add_Hire_Statement_Bulk_Success,
    HireStatementBulkActions.edit_Hire_Statement_Bulk_Success,
    (state) => {
      state.isVisibleAddEditHireStatementBulk = false;
      state.hireStatementBulk = null;
    }
  ),
  immerOn(HireStatementBulkActions.export_Hire_Statement_Bulks, (state) => {
    state.loading.exportBulks = true;
  }),
  immerOn(
    HireStatementBulkActions.export_Hire_Statement_Bulks_Success,
    HireStatementBulkActions.export_Hire_Statement_Bulks_Failure,
    (state) => {
      state.loading.exportBulks = false;
    }
  )
);

export const hireStatementFeature = createFeature({
  name: 'hireStatements',
  reducer: hireStatementReducer,
  extraSelectors: ({ selectLoading }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectCreateEditLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.createEdit
    ),
  }),
});
