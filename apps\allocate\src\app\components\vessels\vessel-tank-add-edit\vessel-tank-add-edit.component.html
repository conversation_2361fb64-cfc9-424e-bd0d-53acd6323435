<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      <h4>{{ vesselTank() ? 'Edit ' : 'Add ' }} Vessel Tank</h4>
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Vessel Tank Name</label>
          <input pInputText type="text" formControlName="name" />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Vessel Tank Name is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Tank Type</label>
          <p-dropdown
            [options]="tankTypes()"
            [filter]="true"
            formControlName="tankTypeId"
            optionLabel="name"
            optionValue="tankTypeId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.tankTypeId?.invalid && controls.tankTypeId?.touched"
          >
            Type is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Bulk Type</label>
          <p-dropdown
            [options]="bulkTypes()"
            [filter]="true"
            formControlName="bulkTypeId"
            optionLabel="name"
            optionValue="bulkTypeId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.bulkTypeId?.invalid && controls.bulkTypeId?.touched"
          >
            Bulk type is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Quantity</label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="3"
            formControlName="quantity"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.quantity?.invalid && controls.quantity?.touched"
          >
            Decimal point should be less than 3
          </small>
        </div>
      </div>

      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Tank Status Change Date</label>
          <p-calendar
            [showTime]="true"
            [showSeconds]="false"
            appendTo="body"
            formControlName="tankStatusChangeDate"
            dateFormat="dd/mm/yy"
            (onClear)="controls.tankStatusChangeDate?.setValue(null)"
            [minDate]="controls.tankStatusChangeDate?.value!"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="
              controls.tankStatusChangeDate?.invalid &&
              controls.tankStatusChangeDate?.touched
            "
          >
            Tank Status Change Date is required
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Day Rate Price</label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            formControlName="dayRatePrice"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.dayRatePrice?.invalid && controls.dayRatePrice?.touched
            "
          >
            Decimal point should be less than 2
          </small>
        </div>
      </div>

      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 align-items-center gap-4">
          <p-inputSwitch formControlName="cleaned" />
          <label class="fs-14">Cleaned</label>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ vesselTank() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
