import { inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DeckPlanActions } from '../actions/deck-plan.actions';
import { VoyageCargoService } from '../../../../../../libs/services/src/lib/services/voyages/voyage-cargo.service';
import { VesselActions } from 'libs/services/src/lib/services/vessels/store/actions/vessels.actions';
import { Store } from '@ngrx/store';
import { deckPlanFeature } from '../features/deck-plan.feature';
import { FlowActions } from '../../../../../../libs/services/src/lib/services/voyages/store/actions/flow.actions';

export const initialize_Deck_Plan = createEffect(
  (actions$ = inject(Actions)) => {
    return actions$.pipe(
      ofType(DeckPlanActions.initialize_Deck_Plan),
      switchMap(({ voyageId, vesselId }) =>
        of(
          DeckPlanActions.load_Deck_Plan({
            voyageId: voyageId!,
          }),
          VesselActions.load_Vessel({
            vesselId: vesselId,
          }),
          FlowActions.load_Voyage_Details({ voyageId })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const load_Deck_Plan = createEffect(
  (actions = inject(Actions), service = inject(VoyageCargoService)) => {
    return actions.pipe(
      ofType(DeckPlanActions.load_Deck_Plan),
      switchMap(({ voyageId }) =>
        service.getAllDeckPlanCargoesByVoyageId(voyageId).pipe(
          map((deckPlan) =>
            DeckPlanActions.load_Deck_Plan_Success({
              deckPlan,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckPlanActions.load_Deck_Plan_Error({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const update_Deck_Plan = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    service = inject(VoyageCargoService)
  ) => {
    return actions.pipe(
      ofType(DeckPlanActions.update_Deck_Plan),
      concatLatestFrom(() => store.select(deckPlanFeature.selectDeckPlan)),
      switchMap(([{ voyageId }, deckPlan]) =>
        service.updateDeckPlanPositions(voyageId, deckPlan!).pipe(
          map(() =>
            DeckPlanActions.update_Deck_Plan_Success({
              successMessage: 'Deck plan saved successfully',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckPlanActions.update_Deck_Plan_Error({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);
