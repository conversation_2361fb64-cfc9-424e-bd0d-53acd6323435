<div class="container fs-14 fw-400">
  <div class="fs-14 d-flex align-items-center gap-8 mb-12">
    <img
      src="assets/arrow.svg"
      alt="users-plus"
      style="margin-right: 4px; height: 20px; width: 20px"
    />
    <b>{{ headerText }}</b>
  </div>
  <p class="fs-14 d-flex flex-direction-column gap-4">
    Select mandatory fields to create a request.
  </p>
  <form [formGroup]="form">
    <div *ngIf="!hasOnlyCliRoleInPlan()">
      <div class="d-flex align-items-center justify-content-start mt-20 mb-12">
        <div class="col">
          <b lass="fs-16 fw-bold"
            >Select client <span style="color: red">*</span></b
          >
        </div>
      </div>
      <p-dropdown
        [options]="clients"
        [showClear]="true"
        [filter]="true"
        optionLabel="clientName"
        optionValue="clientId"
        formControlName="clientId"
        [placeholder]="'Select a client'"
        styleClass="new-version"
        panelStyleClass="new-version-panel"
      >
      </p-dropdown>
      <small
        class="validation-control-error"
        *ngIf="
          form.controls.clientId.hasError('required') &&
          form.controls.clientId.touched
        "
      >
        Client selection is required
      </small>
    </div>
    <div class="d-flex align-items-center justify-content-start mb-6 mt-12">
      <b lass="fs-16 fw-bold">Time <span style="color: red">*</span></b>
    </div>
    <div class="date-time-container">
      <div class="d-flex align-items-center">
        <b class="fs-14 fw-bold">Flexible Timing</b>
      </div>
      <div class="sub-header">
        <span class="fs-13 d-flex justify-content-between align-items-center"
          >Choose if delivery or return time is flexible
          <p-inputSwitch formControlName="isFlexableTiming"
        /></span>
      </div>
      <hr class="hr" />
      <div class="d-flex align-items-center mt-20">
        <b class="fs-14 fw-bold"
          >Arrival Time <span style="color: red">*</span></b
        >
      </div>
      <div class="sub-header">
        <span>Time vessel requested at cluster</span>
        <p-calendar
          formControlName="arrivalTime"
          [showTime]="true"
          [minDate]="today"
          [showIcon]="true"
          dateFormat="dd/mm/yy"
          hourFormat="24"
          [placeholder]="'Select Arrival Time'"
        ></p-calendar>
        <small
          class="p-error"
          *ngIf="
            form.controls.arrivalTime.hasError('required') &&
            form.controls.arrivalTime.touched
          "
        >
          Arrival time is required
        </small>
      </div>
      <div *ngIf="!form.value.isFlexableTiming">
        <div class="d-flex align-items-center mt-12">
          <b class="fs-14 fw-bold">First Installation Time</b>
        </div>
        <div class="sub-header">
          <p-calendar
            formControlName="firstInstallationTime"
            [showTime]="true"
            [minDate]="today"
            [showIcon]="true"
            dateFormat="dd/mm/yy"
            hourFormat="24"
            [placeholder]="'Select first installation time'"
          ></p-calendar>
          <ng-container
            *ngIf="form.controls.firstInstallationTime.errors as errors"
          >
            <small
              class="validation-control-error"
              *ngIf="
                (errors['minDate'] || errors['sameTime']) &&
                form.controls.firstInstallationTime.invalid &&
                form.controls.firstInstallationTime.touched
              "
            >
              First Installation Time must be different from Arrival Time.
            </small>
            <small
              class="validation-control-error"
              *ngIf="
                !(errors['minDate'] || errors['sameTime']) &&
                form.controls.firstInstallationTime.invalid &&
                form.controls.firstInstallationTime.touched
              "
            >
              Please select the first installation time before proceeding
            </small>
          </ng-container>
        </div>
      </div>
      <div
        *ngIf="hasOnlyCliRoleInPlan()"
        class="d-flex align-items-center justify-content-start mb-12 recurrence-row mb-20"
      >
        <div class="input-container">
          <p-dropdown
            [ngClass]="
              form.controls.doesRepeat.value === 'DoesRepeat' ? 'select' : ''
            "
            formControlName="doesRepeat"
            [options]="repeatOptions"
            placeholder="Does Not Repeat"
            (onChange)="setDoesRepeat($event.value)"
            styleClass="new-version"
            optionLabel="label"
            optionValue="value"
          ></p-dropdown>
          <button
            pButton
            *ngIf="form.value.doesRepeat === 'DoesRepeat'"
            (click)="openRepeatDialog()"
          >
            <i class="pi pi-pencil" style="color: #a80303"></i>
          </button>
        </div>
      </div>
      <div *ngIf="form.value.isFlexableTiming">
        <div class="d-flex align-items-center mt-12">
          <b class="fs-14 fw-bold">Latest Arrival Time at First Installation</b>
        </div>
        <div class="sub-header">
          <p-calendar
            formControlName="latestArrivalTime"
            [showTime]="true"
            [minDate]="form.value.arrivalTime!"
            [showIcon]="true"
            dateFormat="dd/mm/yy"
            hourFormat="24"
            [placeholder]="'Select latest arrival time'"
          ></p-calendar>

          <ng-container
            *ngIf="form.controls.latestArrivalTime.errors as errors"
          >
            <small
              class="validation-control-error"
              *ngIf="
                (errors['minDate'] || errors['sameTime']) &&
                form.controls.latestArrivalTime.invalid &&
                form.controls.latestArrivalTime.touched
              "
            >
              Latest Arrival Time must be different from Arrival Time.
            </small>
            <small
              class="validation-control-error"
              *ngIf="
                !(errors['minDate'] || errors['sameTime']) &&
                form.controls.latestArrivalTime.invalid &&
                form.controls.latestArrivalTime.touched
              "
            >
              Please select the latest arrival time before proceeding
            </small>
          </ng-container>
        </div>
      </div>
      <div class="d-flex align-items-center mt-12">
        <b class="fs-14 fw-bold">Cluster Time</b>
      </div>
      <div class="sub-header">
        <input
          pInputText
          type="number"
          formControlName="clusterTime"
          maxlength="250"
          placeholder="Enter value above zero"
        />
        <small
          class="validation-control-error"
          *ngIf="
            form.controls.clusterTime.hasError('greaterThan') &&
            form.controls.clusterTime.touched
          "
        >
          Cluster time should be more than 0
        </small>
        <small
          class="validation-control-error"
          *ngIf="
            form.controls.clusterTime.hasError('decimalPoint') &&
            form.controls.clusterTime.touched
          "
        >
          Decimal point should be less than 2
        </small>
      </div>
      <div class="d-flex align-items-center mt-12">
        <b class="fs-14 fw-bold">Time Unit</b>
      </div>
      <div class="sub-header">
        <span>Select total handling time</span>
      </div>
      <div class="time-unit">
        <div class="toggle-container">
          <input
            type="radio"
            id="hours"
            name="timeUnit"
            value="hours"
            formControlName="timeUnit"
          />
          <label for="hours" class="toggle-label">Hours</label>
          <input
            type="radio"
            id="days"
            name="timeUnit"
            value="days"
            formControlName="timeUnit"
          />
          <label for="days" class="toggle-label">Days</label>
        </div>
      </div>
    </div>

    <div class="d-flex align-items-center justify-content-start mb-12 mt-20">
      <div class="col">
        <h4 class="heading">Other</h4>
      </div>
    </div>

    <div class="date-time-container">
      <div class="d-flex align-items-center">
        <b class="fs-14 fw-bold">Mailbag</b>
      </div>
      <div class="sub-header">
        <span class="fs-14 d-flex justify-content-between align-items-center"
          >Choose if delivery or return time is flexible
          <p-inputSwitch
            class="card flex justify-content-center"
            formControlName="isMailbag"
        /></span>
      </div>
      <div class="d-flex align-items-center">
        <b class="fs-14 fw-bold">Bulk Required</b>
      </div>
      <div class="sub-header">
        <span class="fs-14 d-flex justify-content-between align-items-center"
          >Time vessel requested at cluster
          <p-inputSwitch formControlName="isBulkReq" (change)="toggleBulkReq()"
        /></span>
      </div>
    </div>

    <div class="d-flex align-items-center justify-content-start mt-20">
      <b class="fs-14 fw-bold">Add services</b>
    </div>
    <div class="sub-header">
      <p-multiSelect
        #multiSelect
        [options]="activityCategoryTypesFlattened"
        formControlName="activityCategoryTypes"
        optionValue="activityCategoryTypeId"
        optionLabel="name"
        optionDisabled="isDisabled"
        [filter]="true"
        [group]="true"
        appendTo="body"
        [showToggleAll]="!multiSelect.isEmpty()"
        [showClear]="!!form.controls.activityCategoryTypes.value?.length"
        (onChange)="onSelectionChange($event.value)"
        styleClass="new-version-multiselect"
        panelStyleClass="new-version-panel p-10"
        placeholder="Add services"
      >
      </p-multiSelect>
    </div>

    <div class="selected-items-container" *ngFor="let item of selectedItems">
      <div class="selected-item">
        <span class="label">{{ item.name }}</span>
        <button
          pButton
          (click)="
            timeClicked(
              item,
              $event,
              item.activityCategoryType.activityCategoryId
            )
          "
          class="p-button-rounded p-button-text p-button-success mr-2"
          [disabled]="item.isDisabled"
        >
          <p class="time-label mt-8">{{ item.hours || 0 }} hr</p>
        </button>
        <button
          pButton
          (click)="notesClicked(item, $event)"
          class="p-button-rounded p-button-text p-button-success mr-2"
          [disabled]="item.isDisabled"
        >
          <img *ngIf="!item.notes" src="assets/note.svg" alt="note" />

          <img
            *ngIf="item.notes"
            src="assets/notes-indicator.svg"
            class="notepad"
          />
        </button>
        <button
          pButton
          (click)="removeSelectedItem(item)"
          class="btn-tertiary mt-8 p-button-rounded p-button-text p-button-success mr-2"
          [disabled]="item.isDisabled"
        >
          <i class="pi pi-times"></i>
        </button>
      </div>
    </div>

    <div class="d-flex align-items-center justify-content-start mt-20">
      <div>
        <b class="fs-14 fw-bold">Client reference</b>
      </div>
    </div>

    <div class="w-100">
      <input
        pInputTextarea
        type="text"
        formControlName="clientReference"
        maxlength="250"
        placeholder="Reference ID or name added"
      />
    </div>

    <div class="d-flex align-items-center justify-content-start mt-20">
      <div class="col">
        <h4 class="heading">Remarks</h4>
      </div>
    </div>
    <div>
      <div class="header">
        <textarea
          id="comment-box"
          rows="10"
          style="resize: none"
          placeholder="Add here..."
          pInputTextarea
          formControlName="remarks"
        >
        </textarea>
      </div>
    </div>
  </form>
</div>
