import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { HireStatementTableFields } from '../enums/hire-statement-table-fields.enum';

export function InitializeHireStatementTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(HireStatementTableFields.tableCheckbox, '', 60),
    new ColumnModel(HireStatementTableFields.type, 'Type', 150, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.isOnHire, 'On Hire', 100, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.deliveryDate, 'Delivery Date', 150, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.deliveryPlace, 'Delivery Location', 180, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.redeliveryDate, 'Redelivery Date', 150, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.dayRate, 'Day Rate', 150, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.redeliveryPlace, 'Redelivery Location', 180, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.duration, 'Duration (days)', 150, {
      sortable: true,
    }),
    new ColumnModel(HireStatementTableFields.actions, '', 100),
  ];
  return columns;
}
