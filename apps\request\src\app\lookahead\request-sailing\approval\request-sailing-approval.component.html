<div class="container">
  <div class="row" style="align-content: center">
    <img
      src="assets/arrow.svg"
      alt="users-plus"
      style="margin-right: 4px; height: 20px; width: 20px"
    />
    <h3>Request Approval</h3>
  </div>
  <p class="sub-header">Select a vessel or take action to plan</p>
  <form [formGroup]="form">
    <div style="margin-top: 20px" class="row">
      <div class="col">
        <h4>Select vessel</h4>
      </div>
    </div>
    <mat-form-field
      class="field"
      appearance="outline"
      hideRequiredMarker="true"
    >
      <mat-label>
        <img src="assets/boat.svg" alt="users-plus" style="margin-right: 8px" />
        <span style="vertical-align: top">Select vessel</span>
      </mat-label>
      <mat-select
        formControlName="vesselId"
        (selectionChange)="updateInboundOutbound()"
        [disabled]="hasCompletedRequests"
      >
        <mat-option>
          <ngx-mat-select-search
            [formControl]="searchVesselControl"
            placeholderLabel="Search..."
            noEntriesFoundLabel="No Results found..."
          ></ngx-mat-select-search>
        </mat-option>
        <mat-option
          class="dropdown-hover"
          *ngFor="let vessel of vessels"
          [value]="vessel.vesselId"
        >
          {{ vessel.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-error style="color: red" *ngIf="hasCompletedRequests">
      Cannot change vessel because there are completed transport requests
    </mat-error>
    <div class="button-container">
      <div class="row">
        <label class="select-voyage">Select Voyage</label>
      </div>
      <div class="row add-ons">
        <div class="col addon-btn">
          <!-- @refactor Remove this parent div below on PrimeNG refactor -->
          <div
            [matTooltip]="
              isInboundRequestComplete
                ? 'Cannot change voyage because inbound transport request is complete'
                : ''
            "
            [matTooltipDisabled]="!isInboundRequestComplete"
          >
            <div
              class="image-container"
              [ngClass]="{
                disabled:
                  !inboundSelection ||
                  vesselNotSelectedOrSeriesSelected() ||
                  isInboundRequestComplete,
                active: inboundVoyageActive,
                hover: inboundVoyageHover
              }"
              (click)="openInboundOutboundSidebar('Inbound')"
              (mouseenter)="inboundVoyageHover = true"
              (mouseleave)="inboundVoyageHover = false"
            >
              <img
                *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
                src="assets/inbound.svg"
                class="unchecked"
              />
              <img
                *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
                src="assets/inbound-hover.svg"
                class="hover-image"
              />
              <img
                *ngIf="inboundVoyageSelected && inboundVoyageActive"
                src="assets/inbound-selected-active.svg"
              />
              <img
                *ngIf="inboundVoyageActive && !inboundVoyageSelected"
                src="assets/inbound-hover.svg"
              />
              <img
                *ngIf="!inboundVoyageActive && inboundVoyageSelected"
                src="assets/inbound-selected.svg"
              />
            </div>
          </div>
        </div>
        <div class="col addon-btn">
          <!-- @refactor Remove this parent div below on PrimeNG refactor -->
          <div
            [matTooltip]="
              isOutboundRequestComplete
                ? 'Cannot change voyage because outbound transport request is complete'
                : ''
            "
            [matTooltipDisabled]="!isOutboundRequestComplete"
          >
            <div
              class="image-container"
              [ngClass]="{
                disabled:
                  !outboundSelection ||
                  vesselNotSelectedOrSeriesSelected() ||
                  isOutboundRequestComplete,
                active: outboundVoyageActive,
                hover: outboundVoyageHover
              }"
              (click)="openInboundOutboundSidebar('Outbound')"
              (mouseenter)="outboundVoyageHover = true"
              (mouseleave)="outboundVoyageHover = false"
            >
              <img
                *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
                src="assets/outbound.svg"
                class="unchecked"
              />
              <img
                *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
                src="assets/outbound-hover.svg"
                class="hover-image"
              />
              <img
                *ngIf="outboundVoyageSelected && outboundVoyageActive"
                src="assets/outbound-selected-active.svg"
              />
              <img
                *ngIf="outboundVoyageActive && !outboundVoyageSelected"
                src="assets/outbound-hover.svg"
              />
              <img
                *ngIf="!outboundVoyageActive && outboundVoyageSelected"
                src="assets/outbound-selected.svg"
              />
            </div>
          </div>
        </div>
      </div>
      <mat-error *ngIf="cannotAssignVoyage()"
        >Cannot assign voyage to series</mat-error
      >
    </div>
    <div class="date-time-container">
      <div class="row header-container">
        <div class="header">
          <img
            src="assets/calendar.svg"
            alt="users-plus"
            style="margin-right: 8px"
          />
          <span>Select date and time</span>
        </div>
      </div>
      <div class="row">
        <label class="date-label">Start <span class="required">*</span></label>
        <mat-form-field appearance="outline">
          <input
            [min]="getMinStartDate()"
            [max]="timeAfter"
            matInput
            [matDatepicker]="startpicker"
            (dateChange)="changeStartDate($event.value)"
            formControlName="startTime"
            placeholder="DD/MM/YYYY"
            readonly
          />
          <mat-datepicker-toggle
            matIconSuffix
            [for]="startpicker"
          ></mat-datepicker-toggle>
          <mat-datepicker #startpicker></mat-datepicker>
          <mat-error
            class="error"
            *ngIf="form.controls.startTime.hasError('required')"
            >Start date is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="row">
        <label class="date-label">End <span class="required">*</span></label>
        <mat-form-field appearance="outline">
          <input
            [min]="timeBefore || today"
            matInput
            [matDatepicker]="endpicker"
            formControlName="endTime"
            (dateChange)="changeEndDate($event.value)"
            placeholder="DD/MM/YYYY"
            readonly
          />
          <mat-datepicker-toggle
            matIconSuffix
            [for]="endpicker"
          ></mat-datepicker-toggle>
          <mat-datepicker #endpicker></mat-datepicker>
          <mat-error
            class="error"
            *ngIf="form.controls.endTime.hasError('required')"
            >End date is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="row">
        <div class="input-container">
          <label>
            ETA
            <span *ngIf="form.get('inboundVoyageId')?.value" style="color: red"
              >*</span
            >
            <img
              [matTooltip]="'Estimated time of Arrival'"
              src="assets/info.svg"
              alt="users-plus"
            />
          </label>
          <input
            formControlName="eta"
            class="time-input"
            type="time"
            placeholder="HH:MM AM"
          />
        </div>
      </div>
      <div class="row">
        <div class="input-container">
          <label>
            ETD
            <span *ngIf="form.get('outboundVoyageId')?.value" style="color: red"
              >*</span
            >
            <img
              [matTooltip]="'Estimated time of Departure'"
              src="assets/info.svg"
              alt="users-plus"
            />
          </label>
          <input
            formControlName="etd"
            type="time"
            placeholder="HH:MM AM"
            class="time-input"
          />
        </div>
      </div>
      <div
        *ngIf="!inboundOutboundVoyageSelected()"
        class="input-container flex-align"
      >
        <mat-select
          [ngClass]="
            this.form.controls.doesRepeat.value === 'DoesRepeat' ? 'select' : ''
          "
          formControlName="doesRepeat"
          placeholder="Does Not Repeat"
          (selectionChange)="setDoesRepeat($event.value)"
        >
          <mat-option value="DoesNotRepeat">Does Not Repeat</mat-option>
          <mat-option value="DoesRepeat">Does Repeat</mat-option>
        </mat-select>
        <button
          mat-icon-button
          color="warn"
          *ngIf="form.value.doesRepeat === 'DoesRepeat'"
          (click)="openRepeatDialog()"
        >
          <mat-icon>edit</mat-icon>
        </button>
      </div>
      <mat-error *ngIf="inboundOutboundVoyageSelected()"
        >Cannot create series when voyage is assigned</mat-error
      >
    </div>
    <div style="margin-top: 20px" class="row">
      <div class="col">
        <h4 class="heading">Add Comment</h4>
      </div>
    </div>
    <div class="date-time-container">
      <div class="header">
        <mat-form-field class="comments-textarea" appearance="outline">
          <textarea
            class="comment-textarea"
            matInput
            placeholder="Add comment..."
            formControlName="comment"
          ></textarea>
        </mat-form-field>
      </div>
    </div>
    <div style="margin-top: 20px" class="row">
      <div class="col">
        <h4 class="heading">Take Action</h4>
      </div>
    </div>
    <!--
    NOTE: This is a workaround for tooltips on disabled buttons in Angular Material.
    During future design changes, this should be refactored to a more elegant solution with PrimeNG.
    The div around each button will likely not be necessary.
    Source: https://stackoverflow.com/a/48277910
    -->
    <!-- @refactor please remove .action-button:disabled from scss when converting this file to primeNG -->
    <div class="action-buttons">
      <div class="action-container">
        <div
          matTooltip="Cannot delete sailing requests when some of its transport requests are complete"
          [matTooltipDisabled]="!hasCompletedRequests"
        >
          <button
            mat-icon-button
            class="action-button delete"
            [ngClass]="{ active: selectedStatus === 3 }"
            (click)="selectStatus(3)"
            [disabled]="hasCompletedRequests"
          >
            <mat-icon class="cancel-icon">cancel</mat-icon>
          </button>
        </div>
        <span>Delete</span>
      </div>
      <div class="action-container">
        <div
          matTooltip="Cannot decline sailing requests when some of its transport requests are complete"
          [matTooltipDisabled]="!hasCompletedRequests"
        >
          <button
            mat-icon-button
            class="action-button decline"
            [ngClass]="{ active: selectedStatus === 2 }"
            (click)="selectStatus(2)"
            [disabled]="hasCompletedRequests"
          >
            <mat-icon class="decline-icon">error</mat-icon>
          </button>
        </div>
        <span>Decline</span>
      </div>
      <div class="action-container">
        <div
          matTooltip="Cannot approve sailing requests when some of its transport requests are complete"
          [matTooltipDisabled]="!hasCompletedRequests"
        >
          <button
            mat-icon-button
            class="action-button approve"
            [ngClass]="{ active: selectedStatus === 1 }"
            (click)="selectStatus(1)"
            [disabled]="hasCompletedRequests"
          >
            <mat-icon class="approve-icon">check_circle</mat-icon>
          </button>
        </div>
        <span>Approve</span>
      </div>
    </div>
    <hr class="horizontal-line" />
  </form>
</div>
