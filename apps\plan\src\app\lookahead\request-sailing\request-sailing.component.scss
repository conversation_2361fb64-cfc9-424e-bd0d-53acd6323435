.form-start-label {
  width: 50px;
}

.select {
  background-color: white;
  color: #00729e;
}

.sub-header {
  margin-bottom: 32px;
}

.row.recurrence-row {
  margin-top: 40px;
}

.red-circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  margin-left: 2px;
  margin-bottom: 18px;
}

.next-button {
  float: right;
  margin-top: 16px;
}

.field {
  width: 100%;
  margin-right: 16px;
  margin-top: 5px;
  line-height: 20px;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 16px;
}

.dropdown-hover {
  &:hover {
    color: red;
  }
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 20px;
}

.button-row button {
  margin: 0 10px;
}

button.warn {
  background-color: #f44336;
  color: white;
}


button.transparent:hover {
  text-decoration: underline;
}

.toggle-button {
  border: none;
  background-color: transparent;
  cursor: pointer;
}

button.toggle-button.disabled,
button.toggle-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: all !important;
}

.select-image {
  width: 70px;
  height: 69px;
}
