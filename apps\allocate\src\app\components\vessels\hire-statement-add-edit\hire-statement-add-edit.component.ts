import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
} from '@angular/core';
import { NgIf } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { CalendarModule } from 'primeng/calendar';
import { InputNumberModule } from 'primeng/inputnumber';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputSwitchModule } from 'primeng/inputswitch';

import { HireStatement } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement.interface';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { HireStatementActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement.actions';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';

@Component({
  selector: 'lha-hire-statement-add-edit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf,
    DialogModule,
    CalendarModule,
    InputNumberModule,
    DropdownModule,
    InputTextModule,
    InputSwitchModule,
  ],
  templateUrl: './hire-statement-add-edit.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HireStatementAddEditComponent {
  private readonly store = inject(Store);

  isVisible = this.store.selectSignal(
    hireStatementFeature.selectIsVisibleAddEditHireStatement
  );
  hireStatement = this.store.selectSignal(
    hireStatementFeature.selectHireStatement
  );
  vesselId = this.store.selectSignal(vesselsFeature.selectVesselId);

  form = new FormGroup({
    type: new FormControl<string>('', [Validators.required]),
    isOnHire: new FormControl<boolean>(true, [Validators.required]),
    deliveryDate: new FormControl<Date | null>(null, [Validators.required]),
    deliveryPlace: new FormControl<string>('', [Validators.required]),
    redeliveryDate: new FormControl<Date | null>(null, [Validators.required]),
    redeliveryPlace: new FormControl<string>('', [Validators.required]),
    dayRate: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
  });

  initialValue = this.form.getRawValue();
  controls = {
    type: this.form.get('type'),
    dayRate: this.form.get('dayRate'),
    deliveryDate: this.form.get('deliveryDate'),
    redeliveryDate: this.form.get('redeliveryDate'),
    deliveryPlace: this.form.get('deliveryPlace'),
    redeliveryPlace: this.form.get('redeliveryPlace'),
    isOnHire: this.form.get('isOnHire'),
  };

  typeList = [
    {
      name: 'Spot',
    },
    {
      name: 'Term',
    },
  ];

  constructor() {
    effect(
      () => {
        if (this.isVisible()) {
          if (this.hireStatement()) {
            this.form.patchValue({
              ...this.hireStatement(),
              deliveryDate: this.hireStatement()?.deliveryDate
                ? new Date(this.hireStatement()!.deliveryDate!)
                : null,
              redeliveryDate: this.hireStatement()?.redeliveryDate
                ? new Date(this.hireStatement()!.redeliveryDate!)
                : null,
            });
          }
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  hideDialog() {
    this.store.dispatch(
      HireStatementActions.change_visibility_add_edit({
        visible: false,
        hireStatement: null,
      })
    );
    this.resetForm();
  }

  resetForm() {
    this.form.reset(this.initialValue);
    this.form.updateValueAndValidity();
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      vesselId: this.vesselId(),
    } as HireStatement;

    if (this.hireStatement()) {
      this.store.dispatch(
        HireStatementActions.edit_Hire_Statement({
          hireStatementId: this.hireStatement()?.hireStatementId!,
          hireStatement: model,
          vesselId: this.vesselId(),
        })
      );
    } else {
      this.store.dispatch(
        HireStatementActions.add_Hire_Statement({
          hireStatement: model,
          vesselId: this.vesselId(),
        })
      );
    }
  }
}
