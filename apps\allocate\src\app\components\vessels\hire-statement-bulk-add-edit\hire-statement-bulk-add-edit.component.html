<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ hireStatementBulk() ? 'Edit' : 'Add' }} Hire Statement Bulk
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Bulk Type</label>
          <p-dropdown
            [options]="bulkTypes()"
            [filter]="true"
            formControlName="bulkTypeId"
            optionLabel="name"
            optionValue="bulkTypeId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.bulkTypeId?.invalid && controls.bulkTypeId?.touched"
          >
            Bulk Type is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Start Quantity</label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="3"
            [min]="0"
            formControlName="startQuantity"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.startQuantity?.invalid && controls.startQuantity?.touched
            "
          >
            {{
              controls.startQuantity?.hasError('required')
                ? 'Start Quantity is required.'
                : 'Start Quantity should be more than 0'
            }}
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Price</label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="price"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.price?.invalid && controls.price?.touched"
          >
            {{
              controls.price?.hasError('required')
                ? 'Price is required.'
                : 'Price should be 0 or more'
            }}
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label for="dateLoaded" class="fs-14">Date Loaded</label>
          <p-calendar
            [showTime]="true"
            [showSeconds]="false"
            appendTo="body"
            formControlName="dateLoaded"
            dateFormat="dd/mm/yy"
            (onClear)="controls.dateLoaded?.setValue(null)"
            [maxDate]="maxDate()"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="controls.dateLoaded?.invalid && controls.dateLoaded?.touched"
          >
            Date Loaded is required
          </small>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ hireStatementBulk() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
