import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  effect,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgIf } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { Store } from '@ngrx/store';
import { skip } from 'rxjs';

import { InputSwitchModule } from 'primeng/inputswitch';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';

import { VesselTank } from 'libs/services/src/lib/services/vessels/interfaces/vessel-tank.interface';
import { vesselTanksFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { VesselTankActions } from 'libs/services/src/lib/services/vessels/store/actions/vessel-tank.actions';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';

@Component({
  selector: 'lha-vessel-tank-add-edit',
  standalone: true,
  imports: [
    DialogModule,
    ReactiveFormsModule,
    InputTextModule,
    NgIf,
    InputSwitchModule,
    CalendarModule,
    DropdownModule,
    InputNumberModule,
  ],
  templateUrl: './vessel-tank-add-edit.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VesselTankAddEditComponent implements OnInit {
  private readonly store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  private readonly route = inject(ActivatedRoute);

  isVisible = this.store.selectSignal(
    vesselTanksFeature.selectIsVisibleAddEdit
  );

  vesselTank = this.store.selectSignal(vesselTanksFeature.selectVesselTank);
  tankTypes = this.store.selectSignal(vesselTanksFeature.selectTankTypes);
  bulkTypes = this.store.selectSignal(vesselTanksFeature.selectBulkTypes);
  vesselId = this.route.snapshot.params['vesselId'];

  form = new FormGroup({
    tankTypeId: new FormControl<string>('', [Validators.required]),
    bulkTypeId: new FormControl<string>('', [Validators.required]),
    name: new FormControl<string>('', [Validators.required]),
    quantity: new FormControl<number | null>(null, [
      greaterThan(-1),
      decimalPoint(3),
    ]),
    tankStatusChangeDate: new FormControl<Date | null>(null),
    cleaned: new FormControl<boolean>(false, [Validators.required]),
    dayRatePrice: new FormControl<number | null>(null, [
      greaterThan(-1),
      decimalPoint(2),
    ]),
  });
  initialValue = this.form.getRawValue();

  controls = {
    bulkTypeId: this.form.get('bulkTypeId'),
    tankTypeId: this.form.get('tankTypeId'),
    cleaned: this.form.get('cleaned'),
    name: this.form.get('name'),
    tankStatusChangeDate: this.form.get('tankStatusChangeDate'),
    quantity: this.form.get('quantity'),
    dayRatePrice: this.form.get('dayRatePrice'),
  };

  constructor() {
    effect(
      () => {
        if (this.isVisible()) {
          this.resetForm();
          if (this.vesselTank()) {
            this.form.patchValue({
              ...this.vesselTank(),
              tankStatusChangeDate: this.vesselTank()?.tankStatusChangeDate
                ? new Date(this.vesselTank()!.tankStatusChangeDate!)
                : null,
            });
          }
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit(): void {
    this.controls.cleaned?.valueChanges
      .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
      .subscribe((cleaned) => {
        if (this.vesselTank()) {
          if (cleaned) {
            this.controls.tankStatusChangeDate?.setValidators(
              Validators.required
            );
          } else {
            this.controls.tankStatusChangeDate?.setValidators(null);
          }
        } else if (this.vesselTank()) {
          if (cleaned !== this.vesselTank()?.cleaned) {
            this.controls.tankStatusChangeDate?.setValidators(
              Validators.required
            );
          } else {
            this.controls.tankStatusChangeDate?.setValidators(null);
          }
        }
        this.controls.tankStatusChangeDate?.updateValueAndValidity();
      });
  }

  hideDialog() {
    this.store.dispatch(
      VesselTankActions.change_visibility_add_edit({
        visible: false,
        vesselTank: null,
      })
    );
    this.resetForm();
  }

  resetForm() {
    this.form.reset(this.initialValue);
    this.form.updateValueAndValidity();
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      vesselId: this.vesselId,
      dayRatePrice: this.form.value.dayRatePrice ?? 0,
      quantity: this.form.value.quantity ?? 0,
    } as VesselTank;

    if (this.vesselTank()) {
      this.store.dispatch(
        VesselTankActions.edit_Vessel_Tank({
          vesselTankId: this.vesselTank()?.vesselTankId!,
          vesselTank: model,
          vesselId: this.vesselId,
        })
      );
    } else {
      this.store.dispatch(
        VesselTankActions.add_Vessel_Tank({
          vesselTank: model,
          vesselId: this.vesselId,
        })
      );
    }
  }
}
