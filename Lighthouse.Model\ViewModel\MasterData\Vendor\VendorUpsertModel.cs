﻿namespace Lighthouse.Model.ViewModel.MasterData.Vendor
{
    public class VendorUpsertModel
    {
        public Guid LocationId { get; set; }

        public Guid? VoyageId { get; set; }

        public string VendorName { get; set; }

        public string Address { get; set; }

        public string PostCode { get; set; }

        public string City { get; set; }

        public string Country { get; set; }
        public List<VendorWarehouseModel> VendorWarehouses { get; set; }
        public bool IsPeterson { get; set; }
    }
}
