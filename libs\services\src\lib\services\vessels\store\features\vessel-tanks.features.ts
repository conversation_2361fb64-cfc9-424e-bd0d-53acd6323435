import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { VesselTankActions } from '../actions/vessel-tank.actions';
import { vesselTankInitialState } from '../state/vessel-tank.state';

export const vesselTankReducer = createReducer(
  vesselTankInitialState,
  immerOn(
    VesselTankActions.remove_Vessel_Tank,
    VesselTankActions.load_Vessel_Tanks_Lists,
    (state) => {
      state.loading.list = true;
    }
  ),
  immerOn(
    VesselTankActions.load_Vessel_Tanks_Lists_Success,
    (state, { vesselTanks }) => {
      state.vesselTanks = vesselTanks;
      state.loading.list = false;
    }
  ),
  immerOn(
    VesselTankActions.load_Vessel_Tank_Types_Success,
    (state, { tankTypes }) => {
      state.tankTypes = tankTypes;
      state.loading.list = false;
    }
  ),
  immerOn(
    VesselTankActions.load_Vessel_Bulk_Types_Success,
    (state, { bulkTypes }) => {
      state.bulkTypes = bulkTypes;
      state.loading.list = false;
    }
  ),
  immerOn(
    VesselTankActions.load_Vessel_Tanks_Lists_Failure,
    VesselTankActions.remove_Vessel_Tank_Success,
    VesselTankActions.remove_Vessel_Tank_Failure,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    VesselTankActions.add_Vessel_Tank,
    VesselTankActions.edit_Vessel_Tank,
    (state) => {
      state.loading.createEdit = true;
    }
  ),
  immerOn(
    VesselTankActions.add_Vessel_Tank_Success,
    VesselTankActions.add_Vessel_Tank_Failure,
    VesselTankActions.edit_Vessel_Tank_Success,
    VesselTankActions.edit_Vessel_Tank_Failure,
    (state) => {
      state.loading.createEdit = false;
    }
  ),
  immerOn(VesselTankActions.export_Vessel_Tanks, (state) => {
    state.loading.export = true;
  }),
  immerOn(
    VesselTankActions.export_Vessel_Tanks_Success,
    VesselTankActions.export_Vessel_Tanks_Failure,
    (state) => {
      state.loading.export = false;
    }
  ),
  immerOn(VesselTankActions.change_visibility_add_edit, (state, payload) => {
    state.isVisibleAddEdit = payload.visible;
    state.vesselTank = payload.vesselTank;
  }),
  immerOn(
    VesselTankActions.add_Vessel_Tank_Success,
    VesselTankActions.edit_Vessel_Tank_Success,
    (state) => {
      state.isVisibleAddEdit = false;
      state.vesselTank = null;
    }
  )
);

export const vesselTanksFeature = createFeature({
  name: 'vesselTanks',
  reducer: vesselTankReducer,
  extraSelectors: ({ selectLoading }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectCreateEditLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.createEdit
    ),
  }),
});
