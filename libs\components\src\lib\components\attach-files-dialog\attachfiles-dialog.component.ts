import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AttachedFilesComponent } from '../attached-files/attached-files.component';
import { DialogModule } from 'primeng/dialog';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { VoyageAttachmentType } from 'libs/services/src/lib/services/voyages/enums/voyage-attachment-type.enum';

@Component({
  standalone: true,
  templateUrl: 'attachfiles-dialog.component.html',
  styleUrls: ['attachfiles-dialog.component.scss'],
  selector: 'attach-files-dialog',
  imports: [
    AttachedFilesComponent,
    DialogModule,
    ReactiveFormsModule,
    NgIf,
    ProgressSpinnerModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AttachFilesDialogComponent{
  @Input() entityId: string = ''
  @Input() header: string = ''
  @Input() attachments: any = []
  @Input() cargoAttachments: any = []
  @Input() dialogVisible: boolean = false;
  @Input() transportRequest: boolean = false;
  @Input() viewOnly: boolean = false;
  @Input() gridTemplateColumns: string = 'repeat(3, 1fr)'
  @Input() isLoading = false;
  @Input() filesAccept: string = '*/*'
  @Input() maxFileCount: number | undefined = undefined
  @Input() withSave: boolean = true
  @Input() attachmentTypes: VoyageAttachmentType[] = []
  
  @Input() bulkUpload: boolean = false;
  @Input() uploadFileFn?: (file: File, entityIds: string) => void = () => {};
  @Input() uploadFileBulkFn?: (file: File[], entityIds: string) => void = () => {};
  @Input() removeFileFn: (file: any, entityId: string) => void = () => {};
  @Input() downloadFileFn: (file: any, entityId: string) => void = () => {};

  @Output() dialogToggle = new EventEmitter<void>();
  @Output() saveAttachments = new EventEmitter<{ file: any; checked: boolean }[]>();
  filesToEmit: { file: any; checked: boolean }[] = []


  attachmentsToSave: any[] = [];

  form = new FormGroup({});

  save(event: any) {
    if (this.transportRequest) {
      this.saveAttachments.emit(this.filesToEmit);
    }
    this.attachmentsToSave = [];
  }

  fileChecked(event: any) {
    const existingFile = this.attachmentsToSave.find(file => file.file === event.file);

    if (existingFile) {
      existingFile.checked = event.checked;
    } else {
      this.attachmentsToSave.push({ file: event.file, checked: event.checked });
    }

    this.filesToEmit = this.attachmentsToSave.map(file => ({ file: file.file, checked: file.checked }));

  }

  hideDialog() {
    this.attachmentsToSave = [];
    this.dialogToggle.emit();
  }


}
