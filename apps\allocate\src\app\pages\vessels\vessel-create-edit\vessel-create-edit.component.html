<p-card
  [formGroup]="form"
  [ngClass]="{
  'viewMode': pageMode === mode.View,
}"
>
  <ng-template pTemplate="header">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex flex-direction-column gap-4">
        <lha-upload-img
          [viewMode]="pageMode !== mode.View"
          [isAdd]="pageMode === mode.Create"
          [hidePreview]="true"
          [maxImgSize]="maxImgSize"
          [formControl]="imgControl"
        ></lha-upload-img>
        <small
          class="validation-control-error"
          *ngIf="imgControl.hasError('maxImageSize')"
          >Maximum size of image 2mb
        </small>
        <small
          class="validation-control-error"
          *ngIf="imgControl.hasError('fileTypes')"
          >You can only upload an image of the following formats: .jpg, .png
        </small>
      </div>

      <div class="d-flex justify-content-end gap-8">
        <div
          class="d-flex gap-4 align-items-center"
          *ngIf="pageMode === mode.Edit"
        >
          <p-inputSwitch formControlName="inactive" inputId="inactive" />
          <label class="fs-14 cursor-pointer" for="inactive">
            {{ controls.inactive?.value ? 'Active' : 'Inactive' }}
          </label>
        </div>

        <button
          class="btn-tertiary"
          type="button"
          *ngIf="pageMode !== mode.View"
          (click)="cancel()"
        >
          Cancel
        </button>
        <button
          class="btn-primary"
          type="button"
          *ngIf="pageMode !== mode.View"
          (click)="save()"
        >
          Save
        </button>
        <button
          class="btn-primary"
          type="button"
          *ngIf="pageMode === mode.View"
          (click)="edit()"
        >
          Edit
        </button>
      </div>
    </div>
  </ng-template>

  <div class="d-flex flex-wrap gap-20">
    <div
      class="flex-1 d-flex flex-direction-column gap-20"
      [style.minWidth.px]="460"
    >
      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">IMO</label>

        <div class="flex-1 d-flex flex-direction-column">
          <input pInputText type="text" formControlName="imo" />
          <small
            class="validation-control-error"
            *ngIf="controls.imo?.invalid && controls.imo?.touched"
          >
            IMO is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Vessel Name</label>

        <div class="flex-1 d-flex flex-direction-column">
          <input pInputText type="text" formControlName="name" />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Vessel Name is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Flag State</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="countryList"
            [filter]="true"
            formControlName="country"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Construction Date</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-calendar
            appendTo="body"
            formControlName="construction"
            dateFormat="dd/mm/yy"
            (onClear)="controls.construction?.setValue(null)"
            [maxDate]="today"
            [showIcon]="true"
          ></p-calendar>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">MMSI</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="0"
            [min]="0"
            formControlName="mmsi"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.mmsi?.invalid && controls.mmsi?.touched"
          >
            {{
              controls.mmsi?.hasError('required')
                ? 'MMSI is required'
                : controls.mmsi?.hasError('greaterThan')
                ? 'MMSI should be more than 0'
                : 'MMSI should be no longer than 9 digits'
            }}
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Vessel Owner</label>

        <div class="flex-1 d-flex flex-direction-column">
          <input pInputText type="text" formControlName="vesselOwner" />
          <small
            class="validation-control-error"
            *ngIf="
              controls.vesselOwner?.invalid && controls.vesselOwner?.touched
            "
          >
            Vessel Owner is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">DP Class</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="dpClassList"
            [filter]="true"
            formControlName="dpClass"
            optionLabel="name"
            optionValue="value"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Fire Fight Class</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="firefightClassList"
            [filter]="true"
            formControlName="fireFightClass"
            optionLabel="name"
            optionValue="value"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">EORI</label>

        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="0"
            [min]="0"
            formControlName="eori"
          />

          <small
            class="validation-control-error"
            *ngIf="controls.eori?.invalid && controls.eori?.touched"
          >
            {{
              controls.eori?.hasError('required')
                ? 'EORI is required'
                : 'EORI should be more than 0'
            }}
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <div class="flex-1 d-flex gap-4 align-items-center">
          <label class="fs-14 cursor-pointer" for="roundedSafeHaven">
            Rounded Safe Haven
          </label>
          <p-inputSwitch
            formControlName="roundedSafeHaven"
            inputId="roundedSafeHaven"
          />
        </div>

        <div class="flex-1 d-flex gap-4 align-items-center">
          <label class="fs-14 cursor-pointer" for="errv"> ERRV </label>
          <p-inputSwitch formControlName="errv" inputId="errv" />
        </div>
      </div>
    </div>

    <div
      class="flex-1 d-flex flex-direction-column gap-20"
      [style.minWidth.px]="460"
    >
      <div class="d-flex gap-8 w-100">
        <label class="flex-1 fs-14 mt-8">Length Overall (LOA)</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="length"
          />

          <small
            class="validation-control-error"
            *ngIf="controls.length?.invalid && controls.length?.touched"
          >
            {{
              controls.length?.hasError('required')
                ? 'Length is required'
                : 'Length should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="lengthUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.lengthUnitName?.invalid &&
              controls.lengthUnitName?.touched
            "
          >
            Length Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Deck Length</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="deckLengthValue"
          />

          <small
            class="validation-control-error"
            *ngIf="
              controls.deckLengthValue?.invalid &&
              controls.deckLengthValue?.touched
            "
          >
            {{
              controls.deckLengthValue?.hasError('required')
                ? 'Deck Length is required'
                : 'Deck Length should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="deckLengthUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deckLengthUnitName?.invalid &&
              controls.deckLengthUnitName?.touched
            "
          >
            Deck Length Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Width</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="width"
          />

          <small
            class="validation-control-error"
            *ngIf="controls.width?.invalid && controls.width?.touched"
          >
            {{
              controls.width?.hasError('required')
                ? 'Width is required'
                : 'Width should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="widthUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.widthUnitName?.invalid && controls.widthUnitName?.touched
            "
          >
            Width Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Deck Width</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="deckWidthValue"
          />

          <small
            class="validation-control-error"
            *ngIf="
              controls.deckWidthValue?.invalid &&
              controls.deckWidthValue?.touched
            "
          >
            {{
              controls.deckWidthValue?.hasError('required')
                ? 'Deck Width is required'
                : 'Deck Width should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="deckWidthUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deckWidthUnitName?.invalid &&
              controls.deckWidthUnitName?.touched
            "
          >
            Deck Width Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Draft</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="draft"
          />

          <small
            class="validation-control-error"
            *ngIf="controls.draft?.invalid && controls.draft?.touched"
          >
            {{
              controls.draft?.hasError('required')
                ? 'Draft is required'
                : 'Draft should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="draftUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.draftUnitName?.invalid && controls.draftUnitName?.touched
            "
          >
            Draft Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Deck Capacity</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="deckCapacity"
          />

          <small
            class="validation-control-error"
            *ngIf="
              controls.deckCapacity?.invalid && controls.deckCapacity?.touched
            "
          >
            {{
              controls.deckCapacity?.hasError('required')
                ? 'Deck Capacity is required'
                : 'Deck Capacity should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="deckCapacityUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deckCapacityUnitName?.invalid &&
              controls.deckCapacityUnitName?.touched
            "
          >
            Deck Capacity Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Gross Tonnage</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="grossTonnage"
          />

          <small
            class="validation-control-error"
            *ngIf="
              controls.grossTonnage?.invalid && controls.grossTonnage?.touched
            "
          >
            {{
              controls.grossTonnage?.hasError('required')
                ? 'Gross Tonnage is required'
                : 'Gross Tonnage should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="grossTonnageUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.grossTonnageUnitName?.invalid &&
              controls.grossTonnageUnitName?.touched
            "
          >
            Gross Tonnage Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Net Tonnage</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="netTonnage"
          />

          <small
            class="validation-control-error"
            *ngIf="controls.netTonnage?.invalid && controls.netTonnage?.touched"
          >
            {{
              controls.netTonnage?.hasError('required')
                ? 'Net Tonnage is required'
                : 'Net Tonnage should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="netTonnageUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.netTonnageUnitName?.invalid &&
              controls.netTonnageUnitName?.touched
            "
          >
            Net Tonnage Unit is required
          </small>
        </div>
      </div>

      <div class="d-flex gap-8">
        <label class="flex-1 fs-14 mt-8">Dead Weight</label>
        <div class="flex-1 d-flex flex-direction-column">
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            [min]="0"
            formControlName="deadWeight"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.deadWeight?.invalid && controls.deadWeight?.touched"
          >
            {{
              controls.deadWeight?.hasError('required')
                ? 'Dead Weight is required'
                : 'Dead Weight should be more than 0'
            }}
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column">
          <p-dropdown
            [options]="units()"
            [filter]="true"
            formControlName="deadWeightUnitName"
            optionLabel="name"
            optionValue="name"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deadWeightUnitName?.invalid &&
              controls.deadWeightUnitName?.touched
            "
          >
            Dead Weight Unit is required
          </small>
        </div>
      </div>
    </div>
  </div>
</p-card>
