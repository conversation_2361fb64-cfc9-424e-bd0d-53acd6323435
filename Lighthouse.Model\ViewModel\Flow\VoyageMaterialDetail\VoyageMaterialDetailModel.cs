﻿namespace Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetail
{
    public class VoyageMaterialDetailModel
    {
        public Guid VoyageMaterialDetailId { get; set; }

        public int RowNumber { get; set; }

        public string UnNo { get; set; }

        public Guid? VoyageCargoId { get; set; }

        public VoyageCargoModel VoyageCargo { get; set; }

        public string VoyageCargoCcuId { get; set; }

        public Guid? VoyageCargoBulkId { get; set; }

        public VoyageCargoBulkModel VoyageCargoBulk { get; set; }

        public Guid? VoyageMaterialDetailDangerousGoodId { get; set; }
        public VoyageMaterialDetailDangerousGoodModel VoyageMaterialDetailDangerousGood { get; set; }

        public string VoyageCargoBulkBulkTypeName { get; set; }

        public Guid VoyageId { get; set; }

        public MaterialDetailStatus Status { get; set; }

        public int Quantity { get; set; }

        public string PackingUnit { get; set; }

        public string MaterialDescription { get; set; }

        public MaterialDetailWHSStatus? WHSStatus { get; set; }

        public string PONo { get; set; }

        public Guid? VendorId { get; set; }

        public string VendorName { get; set; }

        public string Requester { get; set; }

        public string WhsLocation { get; set; }

        public string DestinationLocation { get; set; }

        public DateOnly? CollectDate { get; set; }

        public TimeOnly? CollectTime { get; set; }

        public string Phone { get; set; }

        public MaterialDetailTransportRequest? TransportRequest { get; set; }

        public string POTransport { get; set; }

        public string Comments { get; set; }

        public MaterialDetailCustomsStatus? CustomStatus { get; set; }

        public MaterialDetailCustomsEntryType? CustomsEntryType { get; set; }

        public string DocumentNo { get; set; }

        public string SerialNo { get; set; }

        public double Weight { get; set; }

        public string ManifestNo { get; set; }

        public Guid? TransportRequestMaterialDetailId { get; set; }
        public string Value { get; set; }

        public string Category { get; set; }

        public string ProperShippingName { get; set; }

        public MaterialDetailPackingGroup? PackingGroup { get; set; }

        public MaterialDetailIMOCode? IMOCode { get; set; }

        public string Class { get; set; }

        public string SubClass { get; set; }

        public bool LtdQuantity { get; set; }

        public bool MarinePollutant { get; set; }

        public string COO { get; set; }

        public string CommodityCode { get; set; }

        public string JobCardNumber { get; set; }

        public string WorkOrderNumber { get; set; }

        public string MIVMMT { get; set; }

        public string Currency { get; set; }

        public bool IsCancelled { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string CreatedByName { get; set; }

        public string UpdatedByName { get; set; }

        public string VoyageCargoAssetName { get; set; }

        public string VoyageCargoBulkAssetName { get; set; }

        public string AssetName => string.IsNullOrEmpty(VoyageCargoAssetName) ? VoyageCargoBulkAssetName : VoyageCargoAssetName;

        public Guid? VoyageCargoAssetId { get; set; }

        public Guid? VoyageCargoBulkAssetId { get; set; }

        public Guid AssetId => VoyageCargoAssetId ?? VoyageCargoBulkAssetId.Value;

        public string MeasurementUnit { get; set; }
    }
}
