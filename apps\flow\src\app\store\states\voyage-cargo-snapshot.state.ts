import {
  VoyageCargoSnapshot,
  VoyageCargoSnapshotListItem,
} from '../../shared/interfaces';

export type VoyageCargoSnapshotState = {
  voyageCargoSnapshotList: VoyageCargoSnapshotListItem[];
  source: VoyageCargoSnapshot | null;
  sourceLoading: boolean;
  target: VoyageCargoSnapshot | null;
  targetLoading: boolean;
  loading: boolean;
  canCreateNewVersion: boolean;
  selectedSourceId: string | null;
  selectedTargetId: string | null;
};

export const initialVoyageCargoSnapshotState: VoyageCargoSnapshotState = {
  voyageCargoSnapshotList: [],
  source: null,
  sourceLoading: false,
  target: null,
  targetLoading: false,
  loading: false,
  canCreateNewVersion: false,
  selectedSourceId: null,
  selectedTargetId: null,
};
