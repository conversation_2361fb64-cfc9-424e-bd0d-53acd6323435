﻿namespace Lighthouse.Model.ViewModel.MasterData.Vendor
{
    public class VendorModel
    {
        public Guid VendorId { get; set; }

        public Guid? VoyageId { get; set; }

        public Guid LocationId { get; set; }

        public string LocationName { get; set; }

        public string VendorName { get; set; }
        public string Address { get; set; }

        public string PostCode { get; set; }

        public string City { get; set; }

        public string Country { get; set; }

        public string CreatedByName { get; set; }

        public string UpdatedByName { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }
        public List<VendorWarehouseModel> VendorWarehouses { get; set; }
        public int WarehouseCount { get; set; }
        public bool IsPeterson { get; set; }
    }
}

