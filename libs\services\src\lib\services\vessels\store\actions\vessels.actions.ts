import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { Vessel } from '../../interfaces/vessel.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';

export const VesselActions = createActionGroup({
  source: 'Vessel',
  events: {
    init_vessel_add_edit: emptyProps(),
    load_Vessels: emptyProps(),
    load_Vessels_Success: props<{ vessels: Vessel[] }>(),
    load_Vessels_Failure: errorProps(),

    load_Vessel: props<{ vesselId: string }>(),
    load_Vessel_Success: props<{ vessel: Vessel }>(),
    load_Vessel_Failure: errorProps(),

    remove_Vessel: props<{ id: string }>(),
    remove_Vessel_Success: props<{
      vessel: Vessel;
      successMessage: string;
    }>(),
    remove_Vessel_Failure: errorProps(),

    add_Vessel: props<{ vessel: Vessel; img: Event | string | null }>(),
    add_Vessel_Success: props<{ vessel: Vessel; successMessage: string }>(),
    add_Vessel_Failure: errorProps(),

    edit_Vessel: props<{
      vesselId: string;
      vessel: Vessel;
      img: Event | string | null;
    }>(),
    edit_Vessel_Success: props<{ vessel: Vessel; successMessage: string }>(),
    edit_Vessel_Failure: errorProps(),
    load_Vessel_Picture: props<{ vesselPictureId: string }>(),
    load_Vessel_Picture_Success: props<{
      vesselPicture: Blob;
    }>(),
    load_Vessel_Picture_Failure: errorProps(),

    set_VesselId: props<{ vesselId: string}>(),
  },
});
