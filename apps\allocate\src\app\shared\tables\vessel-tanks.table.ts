import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { VesselTanksTableFields } from '../enums/vessel-tanks-table-fields';

export function InitializeVesselTanksTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(VesselTanksTableFields.name, 'Tank Name', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.tankTypeName, 'Tank Type', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.quantity, 'Quantity', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.tankTypeUnitName, 'Tank Unit', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.bulkTypeName, 'Bulk Type', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.createdByName, 'Created By', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.updatedByName, 'Last Updated By', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.cleaned, 'Cleaned', 100, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.tankStatusChangeDate, 'Tank Status Change Date', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.dayRatePrice, 'Day Rate Price', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselTanksTableFields.actions, '', 100),
  ];
  return columns;
}
