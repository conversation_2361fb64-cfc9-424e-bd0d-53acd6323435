import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { HireStatementBulkTableFields } from '../enums/hire-statement-bulk-table.enum';

export function InitializeHireStatementBulkTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(HireStatementBulkTableFields.bulkTypeName, 'Bulk Type', 120),
    new ColumnModel(
      HireStatementBulkTableFields.startQuantity,
      'Start Quantity',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(
      HireStatementBulkTableFields.endQuantity,
      'End Quantity',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(HireStatementBulkTableFields.price, 'Price', 120, {
      sortable: true,
    }),
    new ColumnModel(
      HireStatementBulkTableFields.dateLoaded,
      'Date Loaded',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(HireStatementBulkTableFields.actions, '', 100),
  ];
  return columns;
}
