import { createActionGroup, props, emptyProps } from '@ngrx/store';
import { HireStatementBulk } from '../../interfaces/hire-statement-bulk.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';

export const HireStatementBulkActions = createActionGroup({
  source: 'Hire Statement Bulk',
  events: {
    load_Hire_Statement_Bulks: props<{
      hireStatementId: string;
    }>(),
    load_Hire_Statement_Bulks_Success: props<{
      hireStatementBulks: HireStatementBulk[];
    }>(),
    load_Hire_Statement_Bulks_Failure: errorProps(),

    remove_Hire_Statement_Bulk: props<{
      hireStatementId: string;
      hireStatementBulkId: string;
    }>(),
    remove_Hire_Statement_Bulk_Success: props<{
      hireStatementId: string;
      successMessage: string;
    }>(),
    remove_Hire_Statement_Bulk_Failure: errorProps(),

    add_Hire_Statement_Bulk: props<{
      hireStatementId: string;
      hireStatementBulk: HireStatementBulk;
    }>(),
    add_Hire_Statement_Bulk_Success: props<{
      hireStatementId: string;
      successMessage: string;
    }>(),
    add_Hire_Statement_Bulk_Failure: errorProps(),

    edit_Hire_Statement_Bulk: props<{
      hireStatementBulkId: string;
      hireStatementBulk: HireStatementBulk;
      hireStatementId: string;
    }>(),
    edit_Hire_Statement_Bulk_Success: props<{
      hireStatementId: string;
      successMessage: string;
    }>(),
    edit_Hire_Statement_Bulk_Failure: errorProps(),

    export_Hire_Statement_Bulks: props<{
      hireStatementId: string;
    }>(),
    export_Hire_Statement_Bulks_Success: emptyProps(),
    export_Hire_Statement_Bulks_Failure: errorProps(),

    change_visibility_add_edit: props<{
      visible: boolean;
      hireStatementBulk: HireStatementBulk | null;
    }>(),
  },
});
