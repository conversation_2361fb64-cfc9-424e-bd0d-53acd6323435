<div *ngIf="isLoading" class="d-flex justify-content-center">
    <p-progressSpinner [styleClass]="'spinner-style'"></p-progressSpinner>
  </div>
  <div *ngIf="!isLoading">
    <div *ngIf="!viewOnly && canManage" class="upload-action-container">
      <strong>{{
        attachments && attachments.length < 1 ? 'No attachments attached' : attachments.length===1? '1 attachment' :
          attachments.length + ' attachments' }}</strong>
          <div class="d-flex align-items-center upload-actions">

            <div class="drop-zone" (dragover)="onDragOver($event)" (drop)="onDrop($event)">
              <i class="pi pi-cloud-upload" ></i>
              <p>Drop attachments here</p>
            </div>
            <p-fileUpload 
              #fileUpload
              [multiple]="true"
              (onSelect)="selectNewAttachment($event)" 
              [customUpload]="true"
              chooseLabel="Upload attachment" 
              class="attachment-style" 
              [accept]="filesAccept"
              [disabled]="!canManage"
            >
            </p-fileUpload>
          </div>
    </div>
    <div class="upload-list" *ngIf="attachments?.length">
      <div class="grid-container" [style.gridTemplateColumns]="gridTemplateColumns">
        <div class="grid-items" *ngFor="let attachment of attachments">
          <div *ngIf="transportRequest && !viewOnly">
            <form [formGroup]="form">
              <p-checkbox
                [formControlName]="attachment.documentName"
                inputId="file-{{ attachment.documentName }}"
                [binary]="true"
                (onChange)="emitCheckedFile(attachment, $event.checked)">
              </p-checkbox>
            </form>

          </div>
          <span *ngIf="transportRequest || viewOnly" class="attachment-name-style" [title]="attachment.documentName  || attachment.fileName">
            {{ (attachment.documentName && attachment.documentName.length>20)? (attachment.documentName | slice:0:20)+'...':(attachment.documentName) }}
            {{ (attachment.fileName && attachment.fileName.length>20)? (attachment.fileName | slice:0:20)+'...':(attachment.fileName) }}
          </span>
          <span  *ngIf="!transportRequest && !viewOnly" class="attachment-name-style" [title]="attachment.documentName  || attachment.fileName">
            {{ (attachment.documentName && attachment.documentName.length>35)? (attachment.documentName | slice:0:35)+'...':(attachment.documentName) }}
            {{ (attachment.fileName && attachment.fileName.length>35)? (attachment.fileName | slice:0:35)+'...':(attachment.fileName) }}
          </span>
          <div class="attachment-actions">
            <button pButton type="button" icon="pi pi-cloud-download" class="download-icon-button"
              (click)="downloadSelectedFile(attachment)">
            </button>
           <button
              pButton
              type="button"
              icon="pi pi-trash"
              class="trash-icon-button"
              *ngIf="
                !viewOnly &&
                attachment.isDeletable &&
                canManage &&
                attachment.attachmentType !== cargoReportAttachmentType
              "
              (click)="removeSelectedFile(attachment)"
            >
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="d-flex justify-content-center align-items-center mt-16" *ngIf="!attachments?.length">
      No attachments attached
    </div>
  </div>
