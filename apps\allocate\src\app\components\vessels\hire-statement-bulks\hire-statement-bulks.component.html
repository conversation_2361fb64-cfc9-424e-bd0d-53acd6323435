<div class="d-flex justify-content-between mb-40">
  <b class="fs-24 flex-1">Hire Statement Bulk</b>

  <div class="d-flex gap-8 flex-1 flex-wrap justify-content-end">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>

    <button
      type="button"
      class="btn-primary"
      (click)="add()"
      [disabled]="isOffHire()"
    >
      Create
    </button>
    <button
      type="button"
      class="btn-export align-items-center d-flex"
      (click)="export()"
    >
      <img src="assets/icons/exel.svg" />
      Export
    </button>
  </div>
</div>

<p-table
  #table
  [columns]="listColumns"
  [value]="hireStatementBulks"
  [scrollable]="true"
  scrollHeight="400px"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [loading]="loading().bulkList"
  [globalFilterFields]="[
    tableFields.bulkTypeName,
    tableFields.startQuantity,
    tableFields.endQuantity,
    tableFields.price,
    tableFields.dateLoaded
  ]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <th
            *ngSwitchCase="tableFields.price"
            scope="col"
            [style.min-width.px]="column.width"
            [style.width.%]="(column.width / tableWidth) * 100"
          >
            <span>Price {{ appSettings()?.currency }}</span>
          </th>
          <th
            *ngSwitchDefault
            [style.min-width.px]="column.width"
            [style.width.%]="(column.width / tableWidth) * 100"
            [pSortableColumn]="column.field"
            [pSortableColumnDisabled]="!column.sortable"
          >
            <span>{{ column.name }}</span>
            <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
          </th>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.dateLoaded">
            {{ row.dateLoaded | date : 'dd/MM/yyyy HH:mm' }}
          </td>

          <td *ngSwitchCase="tableFields.price">
            {{ row.price | number : '1.2-2' }}
          </td>
          <td *ngSwitchCase="tableFields.startQuantity">
            {{ row.startQuantity ?? 'N/A' }}
          </td>
          <td *ngSwitchCase="tableFields.endQuantity">
            {{ row.endQuantity ?? 'N/A' }}
          </td>

          <td *ngSwitchCase="tableFields.actions">
            <div class="d-flex gap-8 flex-wrap">
              <button
                type="button"
                class="btn-icon-only"
                (click)="edit(row)"
                [disabled]="isOffHire()"
              >
                <em class="pi pi-pencil"></em>
              </button>

              <button
                type="button"
                class="btn-icon-only"
                (click)="remove(row)"
                [disabled]="isOffHire()"
              >
                <em class="pi pi-trash"></em>
              </button>
            </div>
          </td>

          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="4" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>

<lha-hire-statement-bulk-add-edit/>
