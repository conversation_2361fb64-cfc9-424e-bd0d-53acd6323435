﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lighthouse.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddAdhocCargoRelatedPropertiesToVendorCargoDescriptionAndCargoTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "<PERSON>Peterson",
                table: "Vendors",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "<PERSON>Adhoc",
                table: "Cargos",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "<PERSON><PERSON>dh<PERSON>",
                table: "CargoDescriptions",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>",
                table: "Vendors");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>",
                table: "Cargos");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>",
                table: "CargoDescriptions");
        }
    }
}
