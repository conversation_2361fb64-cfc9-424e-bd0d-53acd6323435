import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  DatePip<PERSON>,
  NgI<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgS<PERSON>,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';
import { ConfirmationService } from 'primeng/api';

import { vesselTanksFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { VesselTank } from 'libs/services/src/lib/services/vessels/interfaces/vessel-tank.interface';
import { VesselTankActions } from 'libs/services/src/lib/services/vessels/store/actions/vessel-tank.actions';
import { InitializeVesselTanksTable } from '../../../shared/tables/vessel-tanks.table';
import { VesselTanksTableFields } from '../../../shared/enums/vessel-tanks-table-fields';
import { VesselTankAddEditComponent } from '../vessel-tank-add-edit/vessel-tank-add-edit.component';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';

@Component({
  selector: 'lha-tanks',
  standalone: true,
  imports: [
    NgClass,
    DatePipe,
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    NgIf,
    VesselTankAddEditComponent,
  ],
  templateUrl: './tanks.component.html',
})
export class TanksComponent implements OnInit {
  @ViewChild('table') table!: Table;

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  loading = this.store.selectSignal(vesselTanksFeature.selectLoading);
  vesselId = this.store.selectSignal(vesselsFeature.selectVesselId);
  searchValue = '';
  listColumns = InitializeVesselTanksTable();
  vesselTanks: VesselTank[] = [];
  tableFields = VesselTanksTableFields;
  tableWidth = 850;

  ngOnInit(): void {
    this.store.dispatch(
      VesselTankActions.initialize_Vessel_Tanks({ vesselId: this.vesselId() })
    );

    this.actions
      .pipe(
        ofType(VesselTankActions.load_Vessel_Tanks_Lists_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ vesselTanks }) => {
        this.searchValue = '';
        this.vesselTanks = [...vesselTanks];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export(): void {
    this.store.dispatch(
      VesselTankActions.export_Vessel_Tanks({ vesselId: this.vesselId() })
    );
  }

  add(): void {
    this.store.dispatch(
      VesselTankActions.change_visibility_add_edit({
        visible: true,
        vesselTank: null,
      })
    );
  }

  edit(vesselTank: VesselTank): void {
    this.store.dispatch(
      VesselTankActions.change_visibility_add_edit({
        visible: true,
        vesselTank,
      })
    );
  }

  remove(vesselTank: VesselTank): void {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
            Deleting this Vessel Tank may affect other parts of Allocate..
            <br>
            Do you want to remove this Vessel Tank?
          `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          VesselTankActions.remove_Vessel_Tank({
            vesselTankId: vesselTank.vesselTankId,
            vesselId: this.vesselId(),
          })
        );
      },
    });
  }
}
