import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { HireStatement } from './interfaces/hire-statement.interface';
import { HireStatementBulk } from './interfaces/hire-statement-bulk.interface';

@Injectable({
  providedIn: 'root',
})
export class HireStatementService {
  private http = inject(HttpClient);

  loadHireStatementListByVesselId(
    vesselId: string
  ): Observable<HireStatement[]> {
    return this.http.get<HireStatement[]>(
      `/api/hireStatement/byVesselId/${vesselId}`
    );
  }

  loadHireStatement(id: string): Observable<HireStatement> {
    return this.http.get<HireStatement>(`/api/hireStatement/${id}`);
  }

  removeHireStatement(id: string): Observable<HireStatement> {
    return this.http.delete<HireStatement>(`/api/hireStatement/${id}`);
  }

  addHireStatement(hireStatement: HireStatement): Observable<HireStatement> {
    return this.http.post<HireStatement>('/api/hireStatement', hireStatement);
  }

  editHireStatement(
    id: string,
    hireStatement: HireStatement
  ): Observable<HireStatement> {
    return this.http.put<HireStatement>(
      `/api/hireStatement/${id}`,
      hireStatement
    );
  }

  exportHireStatements(vesselId: string): Observable<ArrayBuffer> {
    return this.http.get(
      `/api/hirestatement/exporthirestatements/${vesselId}`,
      {
        responseType: 'arraybuffer',
      }
    );
  }

  loadHireStatementBulkList(id: string): Observable<HireStatementBulk[]> {
    return this.http.get<HireStatementBulk[]>(
      `/api/hirestatementbulk/byhirestatementid/${id}`
    );
  }

  removeHireStatementBulk(id: string): Observable<HireStatementBulk> {
    return this.http.delete<HireStatementBulk>(`/api/hirestatementbulk/${id}`);
  }

  addHireStatementBulk(
    hireStatementBulk: HireStatementBulk
  ): Observable<HireStatementBulk> {
    return this.http.post<HireStatementBulk>(
      '/api/hirestatementbulk',
      hireStatementBulk
    );
  }

  editHireStatementBulk(
    id: string,
    hireStatementBulk: HireStatementBulk
  ): Observable<HireStatementBulk> {
    return this.http.put<HireStatementBulk>(
      `/api/hirestatementbulk/${id}`,
      hireStatementBulk
    );
  }

  exportHireStatementBulks(hireStatementId: string): Observable<ArrayBuffer> {
    return this.http.get(
      `/api/hirestatementbulk/exporthirestatementbulks/${hireStatementId}`,
      {
        responseType: 'arraybuffer',
      }
    );
  }
}
