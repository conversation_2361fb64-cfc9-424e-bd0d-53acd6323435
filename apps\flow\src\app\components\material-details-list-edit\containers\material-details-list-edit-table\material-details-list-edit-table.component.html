<ng-container [formGroup]="form">
  <p-table
    #table
    [value]="materialDetailList()"
    [columns]="listColumns()"
    (selectionChange)="selectedListItems($event)"
    [selection]="selectedItems()"
    [scrollable]="true"
    [lazy]="true"
    [loading]="tableLoading()"
    scrollHeight="400px"
    libCopyDirective
    libPasteTableValue
    (onPaste)="onPaste($event)"
  >
    <ng-container formArrayName="materialDetailList">
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let column of columns">
            <ng-container [ngSwitch]="column.field">
              <th
                *ngSwitchCase="'tableCheckbox'"
                scope="col"
                [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth()) * 100"
              >
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
              </th>

              <th
                *ngSwitchDefault
                scope="col"
                [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth()) * 100"
              >
                <span>{{ column.name }}</span>
                <span *ngIf="column.validator" class="color-primary"> * </span>
              </th>
            </ng-container>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template
        pTemplate="body"
        let-rowData
        let-index="rowIndex"
        let-columns="columns"
      >
        <ng-container
          [formGroupName]="index"
          *ngIf="materialDetailList().length"
        >
          <tr
            [ngClass]="{
            'cancelled-row': rowData.isCancelled,
            'viewMode': rowData.isCancelled,
          }"
          >
            <ng-container *ngFor="let column of columns">
              <ng-container [ngSwitch]="column.field">
                <td
                  *ngSwitchCase="'tableCheckbox'"
                  pFrozenColumn
                  [frozen]="true"
                  alignFrozen="left"
                >
                  <p-tableCheckbox
                    [value]="rowData"
                    (click)="$event.stopPropagation()"
                  ></p-tableCheckbox>
                </td>
                <td *ngSwitchCase="materialDetailTableFields.rowNumber">
                  <div class="d-flex justify-content-between">
                    {{ index + 1 }}
                    <em
                      *ngIf="materialDetailListCtrl.at(index).invalid"
                      class="icon-red-info"
                      pTooltip="Complete all required fields"
                      tooltipPosition="bottom"
                    ></em>
                  </div>
                </td>
                <td
                  *ngSwitchCase="materialDetailTableFields.status"
                  libNoneSelectable
                >
                  {{
                    rowData.status === status.Approved
                      ? 'A'
                      : rowData.status === status.Submitted
                      ? 'S'
                      : 'D'
                  }}
                </td>
                <td *ngSwitchCase="materialDetailTableFields.voyageMaterialDetailDangerousGoodId">
                <span
                  *ngIf="
                    (rowData.voyageCargoCcuId && rowData.voyageMaterialDetailDangerousGoodId) ||
                    (rowData.voyageCargoBulkBulkTypeName && rowData.voyageCargoBulk?.voyageCargoBulkDangerousGood)
                  "
                  class="d-flex cursor-pointer"
                  (click)="showDangerousGoodsDialog(rowData)"
                  appStopPropagatePreventDefault
                >
                  <img width="25" src="assets/danger.svg" />
                </span>
              </td>
                <td
                  *ngSwitchDefault
                  libCellSelect
                  [columnName]="column.field"
                  [rowIndex]="index"
                >
                  <ng-container [ngSwitch]="column.fieldType">
                    <ng-container *ngSwitchCase="fieldType.dropdown">
                      <p-dropdown
                        [options]="dropdownLookups()[column.field]"
                        [formControlName]="column.field"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select"
                        [showClear]="true"
                        styleClass="new-version"
                        appendTo="body"
                        panelStyleClass="new-version-panel"
                        (onChange)="changeDropdown(column.field, index)"
                      />
                    </ng-container>

                    <ng-container *ngSwitchCase="fieldType.number">
                      <p-inputNumber
                        mode="decimal"
                        [formControlName]="column.field"
                        [minFractionDigits]="0"
                        [maxFractionDigits]="column.decimals"
                        placeholder="Enter"
                        [tabindex]="0"
                      ></p-inputNumber>
                    </ng-container>

                    <ng-container *ngSwitchCase="fieldType.checkbox">
                      <p-checkbox
                        [formControlName]="column.field"
                        [binary]="true"
                        [inputId]="column.field"
                      ></p-checkbox>
                    </ng-container>

                    <ng-container *ngSwitchCase="fieldType.datePicker">
                      <p-calendar
                        [showIcon]="true"
                        [tabindex]="0"
                        dateFormat="dd/mm/yy"
                        [inputId]="column.field"
                        [formControlName]="column.field"
                        (keydown)="$event.stopPropagation()"
                        appendTo="body"
                      >
                      </p-calendar>
                    </ng-container>

                    <ng-container *ngSwitchCase="fieldType.timePicker">
                      <p-calendar
                        [showIcon]="true"
                        [tabindex]="0"
                        [timeOnly]="true"
                        [showTime]="true"
                        hourFormat="24"
                        [inputId]="column.field"
                        [formControlName]="column.field"
                        (keydown)="$event.stopPropagation()"
                        appendTo="body"
                      >
                      </p-calendar>
                    </ng-container>

                    <ng-container *ngSwitchDefault>
                      <input
                        pInputText
                        type="text"
                        pInputText
                        [formControlName]="column.field"
                      />
                    </ng-container>
                  </ng-container>
                </td>
              </ng-container>
            </ng-container>
          </tr>
        </ng-container>
      </ng-template>
    </ng-container>
  </p-table>
</ng-container>

<div class="d-flex justify-content-between gap-8 mt-24">
  <button class="btn-secondary" type="button" (click)="add()">Add</button>

  <div class="d-flex gap-8">
    <button
      class="btn-tertiary"
      type="button"
      (click)="cancel()"
      [disabled]="
        materialDetailListCtrl.getRawValue()
          | hasChangeValue : initialFormValue.materialDetailList
      "
    >
      Cancel
    </button>
    <button
      class="btn-primary"
      type="button"
      (click)="save()"
      [disabled]="
        materialDetailListCtrl.invalid ||
        (materialDetailListCtrl.getRawValue()
          | hasChangeValue : initialFormValue.materialDetailList)
      "
    >
      Save
    </button>
  </div>
</div>
