import {
  Component,
  OnInit,
  inject,
  OnDestroy,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  DestroyRef,
  ChangeDetectionStrategy,
  effect,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { lookaheadFeature } from '../../../../../../../libs/services/src/lib/services/lookahead/store/features';
import { Subscription } from 'rxjs';
import {
  FormControl,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormGroup,
} from '@angular/forms';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { CommonModule, DatePipe } from '@angular/common';
import {
  timeAfter,
  timeBefore,
} from '../../../../../../../libs/components/src/lib/functions/utility.functions';
import { MatDialog } from '@angular/material/dialog';
import { Actions } from '@ngrx/effects';
import { PlanningDetailsService } from '../../../shared/services/planning-details.service';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { LookaheadActions } from '../../../../../../../libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import { vesselsFeature } from '../../../../../../../libs/services/src/lib/services/vessels/store/features/vessels.features';
import { RepeatDialogActions } from '../../../store/actions/repeat-dialog.actions';
import { RepeatDialogData } from '../../../shared/types/repeat-dialog-data.interface';
import { Voyage } from '../../../../../../../libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { InputTextModule } from 'primeng/inputtext';
import { repeatDialogFeature } from '../../../store/features/repeat-dialog.feature';
import { stripTimezoneOffset } from '../../../../../../../libs/services/src/lib/services/functions/convert-date.utils';
import { requestPanelFeature } from '../../../../../../../libs/services/src/lib/services/lookahead/store/features/request-panel.feature';
import { RemoveSecondsPipe } from 'libs/components/src/lib/pipes';

@Component({
  selector: 'request-sailing-approval',
  templateUrl: './request-sailing-approval.component.html',
  styleUrls: ['./request-sailing-approval.component.scss'],
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    DropdownModule,
    CalendarModule,
    InputTextareaModule,
    InputTextModule,
  ],
  providers: [RemoveSecondsPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestSailingApprovalComponent
  implements OnInit, OnChanges, OnDestroy
{
  private subscriptions = new Subscription();
  private readonly datePipe = inject(DatePipe);
  private readonly removeSecondsPipe = inject(RemoveSecondsPipe);
  store = inject(Store);
  sharedService = inject(LookaheadSharedService);
  dialog = inject(MatDialog);
  vm$ = this.store.select(lookaheadFeature.selectLookaheadsState);
  @Input() originalVessels: Vessel[] = [];
  @Input() inboundSelection: boolean | null = false;
  @Input() outboundSelection: boolean | null = false;
  @Input() hasCompletedRequests = false;
  @Input() isInboundRequestComplete = false;
  @Input() isOutboundRequestComplete = false;
  @Input() set showErrors(value: boolean) {
    if (value) {
      this.showValidationErrors = true;
    }
  }
  @Input() showApprovalErrors = false;
  @Output() approvalDataChanged = new EventEmitter<any>();
  vessels = this.store.selectSignal(vesselsFeature.selectVessels);
  voyages = this.store.selectSignal(lookaheadFeature.selectVoyages);
  inboundVoyageSelected?: string | null;
  outboundVoyageSelected?: string | null;
  inboundVoyageActive = false;
  outboundVoyageActive = false;
  inboundVoyageHover = false;
  outboundVoyageHover = false;
  timeAfter: Date | null = null;
  timeBefore: Date | null = null;
  minDifference = 1;
  today = new Date();
  editMode = false;
  selectedStatus = 0;
  showValidationErrors = false;
  form = new FormGroup({
    vesselId: new FormControl<string | null>(null),
    inboundVoyageId: new FormControl<string | null>(null),
    outboundVoyageId: new FormControl<string | null>(null),
    comment: new FormControl<string>(''),
    startTime: new FormControl<Date | null>(null, [Validators.required]),
    endTime: new FormControl<Date | null>(null, [Validators.required]),
    eta: new FormControl<string>(''),
    etd: new FormControl<string>(''),
    status: new FormControl<number>(0),
    doesRepeat: new FormControl<string>(''),
    seriesStartTime: new FormControl<Date | null>(null),
    seriesEndTime: new FormControl<Date | null>(null),
    weeklyPattern: new FormControl<string>(''),
    repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
  });
  planningDetailsService = inject(PlanningDetailsService);

  repeatOptions = [
    { label: 'Does Not Repeat', value: 'DoesNotRepeat' },
    { label: 'Does Repeat', value: 'DoesRepeat' },
  ];
  minStartDate!: Date;
  private actions = inject(Actions);
  private destroyRef = inject(DestroyRef);

  panelSailingRequest = this.store.selectSignal(
    requestPanelFeature.selectPanelSailingRequest
  );
  repeatDialogData = this.store.selectSignal(repeatDialogFeature.selectResult);
  selectedInboundVoyage = this.store.selectSignal(
    lookaheadFeature.selectSelectedInboundVoyage
  );
  selectedOutboundVoyage = this.store.selectSignal(
    lookaheadFeature.selectSelectedOutboundVoyage
  );
  outboundVoyages: Voyage[] = [];
  inboundVoyages: Voyage[] = [];

  constructor() {
    effect(
      () => {
        const request = this.panelSailingRequest();
        if (request) {
          this.patchForm(request);
        } else {
          this.resetForm();
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const repeatSchedule = this.repeatDialogData();
        if (repeatSchedule && !this.editMode) {
          const startDate = repeatSchedule.startDate
            ? new Date(repeatSchedule.startDate)
            : null;
          const endDate = repeatSchedule.endDate
            ? new Date(repeatSchedule.endDate)
            : null;

          this.form.patchValue(
            {
              startTime: startDate,
              endTime: endDate,
              seriesStartTime: startDate,
              seriesEndTime: endDate,
              weeklyPattern: repeatSchedule.weeklyPattern,
              repeatEveryNumberOfWeeks: repeatSchedule.repeatEveryNumberOfWeeks,
              doesRepeat: 'DoesRepeat',
            },
            { emitEvent: false }
          );
          this.approvalDataChanged.emit(this.form.value);
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const inboundVoyage = this.selectedInboundVoyage();
        if (inboundVoyage !== null) {
          this.form.get('inboundVoyageId')?.setValue(inboundVoyage);
          this.inboundVoyageSelected = inboundVoyage;
        } else {
          this.form.get('inboundVoyageId')?.setValue(null);
          this.inboundVoyageSelected = null;
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const outboundVoyage = this.selectedOutboundVoyage();
        if (outboundVoyage !== null) {
          this.form.get('outboundVoyageId')?.setValue(outboundVoyage);
          this.outboundVoyageSelected = outboundVoyage;
        } else {
          this.form.get('outboundVoyageId')?.setValue(null);
          this.outboundVoyageSelected = null;
        }
      },
      { allowSignalWrites: true }
    );
  }

  ngOnInit() {
    this.updateValidators();
    this.updateMinStartDate();

    this.loadPlanningDetails();

    this.subscriptions.add(
      this.sharedService.resetRequestSailingFormSubject.subscribe(() => {
        this.resetForm();
      })
    );
    this.subscriptions.add(
      this.form.valueChanges.subscribe((value) => {
        this.approvalDataChanged.emit(value);
      })
    );
    this.updateFormControlsDisabledState();
  }

  loadPlanningDetails() {
    this.planningDetailsService
      .loadInboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null && planningDetails.eta !== null) {
          const etaDate = new Date(planningDetails.eta!);
          const etaTimeString: string = etaDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            startTime: etaDate,
            eta: etaTimeString,
          });
        }
      });

    this.planningDetailsService
      .loadOutboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null && planningDetails.etd !== null) {
          const etdDate = new Date(planningDetails.etd!);
          const etdTimeString: string = etdDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            endTime: etdDate,
            etd: etdTimeString,
          });
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inboundSelection'] || changes['outboundSelection']) {
      if (changes['inboundSelection'] && !this.inboundSelection) {
        this.form.get('inboundVoyageId')?.setValue(null);
      }
      if (changes['outboundSelection'] && !this.outboundSelection) {
        this.form.get('outboundVoyageId')?.setValue(null);
      }
      this.updateValidators();
    }

    if (changes['showApprovalErrors']) {
      this.showValidationErrors = this.showApprovalErrors;
    }
    if (changes['hasCompletedRequests']) {
      this.updateFormControlsDisabledState();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private patchForm(sailingRequest: any): void {
    this.form.patchValue({
      vesselId: sailingRequest.VesselId,
      startTime: sailingRequest.StartTime
        ? new Date(sailingRequest.StartTime)
        : new Date(),
      endTime: sailingRequest.EndTime ? new Date(sailingRequest.EndTime) : null,
      inboundVoyageId: sailingRequest.InboundVoyageId,
      outboundVoyageId: sailingRequest.OutboundVoyageId,
      weeklyPattern: sailingRequest.WeeklyPattern,
      eta: sailingRequest.Eta,
      etd: sailingRequest.Etd,
      comment: sailingRequest.Comment,
      status: sailingRequest.Status,
    });

    this.selectedStatus = sailingRequest.Status;
    this.inboundVoyageSelected = sailingRequest.InboundVoyageId;
    this.outboundVoyageSelected = sailingRequest.OutboundVoyageId;
    this.updateValidators();
    this.updateFormControlsDisabledState();
  }

  vesselNotSelectedOrSeriesSelected(): boolean {
    return (
      !this.form.get('vesselId')?.value ||
      (!this.editMode &&
        this.form.value.seriesStartTime !== null &&
        this.form.value.seriesEndTime !== null)
    );
  }

  openInboundOutboundSidebar(voyageType: string): void {
    const vesselId = this.form.get('vesselId')?.value!;

    if (voyageType === 'Inbound') {
      this.store.dispatch(
        LookaheadActions.update_Selected_Inbound_Voyage({
          voyageId: this.form.get('inboundVoyageId')!.value ?? '',
        })
      );
      this.inboundVoyages = this.voyages().filter(
        (v: any) => v.vesselId === vesselId && v.voyageDirection === 0
      );
      this.inboundVoyageActive = true;
      this.outboundVoyageActive = false;
    } else {
      this.store.dispatch(
        LookaheadActions.update_Selected_Outbound_Voyage({
          voyageId: this.form.get('outboundVoyageId')!.value ?? '',
        })
      );

      this.outboundVoyages = this.voyages().filter(
        (v: any) => v.vesselId === vesselId && v.voyageDirection === 1
      );
      this.inboundVoyageActive = false;
      this.outboundVoyageActive = true;
    }

    this.store.dispatch(
      LookaheadActions.change_Visibility_Inbound_Outbound_Panel({
        visible: true,
        voyageType: voyageType,
        vesselId: vesselId,
        inboundVoyages: this.inboundVoyages,
        outboundVoyages: this.outboundVoyages,
      })
    );
  }

  changeStartDate(value: Date): void {
    const startDateControl = this.form.get('startTime');
    if (startDateControl) {
      startDateControl.setValue(value);
    }

    this.timeBefore = timeBefore(value!, { min: this.minDifference });
    this.form.controls.endTime.setValidators([
      Validators.required,
      Validators.min(value!.getTime()),
    ]);
    this.form.controls.endTime.updateValueAndValidity();
  }

  updateMinStartDate() {
    if (this.editMode) {
      const startTimeValue = this.form.get('startTime')?.value;
      if (startTimeValue instanceof Date) {
        this.minStartDate = startTimeValue;
      } else if (typeof startTimeValue === 'string') {
        const parsedDate = new Date(startTimeValue);
        if (!isNaN(parsedDate.getTime())) {
          this.minStartDate = parsedDate;
        } else {
          this.minStartDate = new Date();
        }
      } else {
        this.minStartDate = new Date();
      }
    } else {
      this.minStartDate = new Date();
    }
  }

  setDoesRepeat(value: string) {
    this.form.get('doesRepeat')?.setValue(value);
    if (value === 'DoesRepeat') {
      this.openRepeatDialog();
    } else {
      this.form.get('seriesStartTime')?.setValue(null);
      this.form.get('seriesEndTime')?.setValue(null);
      this.form.get('weeklyPattern')?.setValue('');
      this.form.get('repeatEveryNumberOfWeeks')?.setValue(null);
    }
  }

  openRepeatDialog() {
    const data: RepeatDialogData = {
      startDate:
        this.form.value.seriesStartTime !== null &&
        this.form.value.seriesStartTime !== undefined
          ? this.form.value.seriesStartTime instanceof Date
            ? this.form.value.seriesStartTime.toISOString()
            : this.form.value.seriesStartTime
          : null,
      endDate:
        this.form.value.seriesEndTime !== null &&
        this.form.value.seriesEndTime !== undefined
          ? this.form.value.seriesEndTime instanceof Date
            ? this.form.value.seriesEndTime.toISOString()
            : this.form.value.seriesEndTime
          : new Date(new Date().getFullYear(), 11, 31).toISOString(),
      repeatEveryNumberOfWeeks: this.form.value.repeatEveryNumberOfWeeks ?? 0,
      weeklyPattern: this.form.value.weeklyPattern ?? '',
    };

    const safeData: RepeatDialogData = {
      startDate: data.startDate ?? null,
      endDate: data.endDate ?? null,
      repeatEveryNumberOfWeeks: data.repeatEveryNumberOfWeeks,
      weeklyPattern: data.weeklyPattern,
    };

    this.store.dispatch(
      RepeatDialogActions.openRepeatDialog({
        data: safeData,
      })
    );
  }

  changeEndDate(value: Date): void {
    this.timeAfter = timeAfter(value, { min: this.minDifference - 1 });
    this.form.controls.startTime.setValidators([Validators.required]);
    this.form.controls.startTime.updateValueAndValidity();
  }

  selectStatus(status: number): void {
    this.selectedStatus = status;
    this.form.get('status')?.setValue(status);
    this.showValidationErrors = true;
  }

  updateInboundOutbound(): void {
    this.sharedService.updateRequestedInboundVoyage(null);
    this.sharedService.updateRequestedOutboundVoyage(null);

    this.inboundVoyageSelected = null;
    this.outboundVoyageSelected = null;
    this.inboundVoyageActive = false;
    this.outboundVoyageActive = false;

    this.updateValidators();
  }

  resetForm(): void {
    this.form.reset();
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.timeAfter = null;
    this.timeBefore = new Date();
    this.selectedStatus = 0;
    this.editMode = false;
    this.inboundVoyageSelected = null;
    this.outboundVoyageSelected = null;
    this.inboundVoyageActive = false;
    this.outboundVoyageActive = false;
    this.inboundVoyageHover = false;
    this.outboundVoyageHover = false;
    this.sharedService.updateRequestedInboundVoyage(null);
    this.sharedService.updateRequestedOutboundVoyage(null);

    this.updateValidators();

    this.hasCompletedRequests = false;
    this.updateFormControlsDisabledState();
  }

  inboundOutboundVoyageSelected() {
    return (
      (!this.editMode && this.form.value.inboundVoyageId !== null) ||
      this.form.value.outboundVoyageId !== null
    );
  }

  cannotAssignVoyage(): boolean {
    return (
      !this.editMode &&
      this.form.value.seriesStartTime !== null &&
      this.form.value.seriesEndTime !== null
    );
  }

  isEtaRequired(): boolean {
    return this.form.get('inboundVoyageId')?.value !== null;
  }

  isEtdRequired(): boolean {
    return this.form.get('outboundVoyageId')?.value !== null;
  }

  updateValidators(): void {
    const etaCtrl = this.form.get('eta');
    const etdCtrl = this.form.get('etd');

    // Store current values before updating validators
    const currentEta = etaCtrl?.value;
    const currentEtd = etdCtrl?.value;

    if (this.isEtaRequired()) {
      etaCtrl?.setValidators([Validators.required]);
    } else {
      etaCtrl?.clearValidators();
    }

    if (this.isEtdRequired()) {
      etdCtrl?.setValidators([Validators.required]);
    } else {
      etdCtrl?.clearValidators();
    }

    // Update validity without changing the values
    etaCtrl?.updateValueAndValidity({ onlySelf: true, emitEvent: false });
    etdCtrl?.updateValueAndValidity({ onlySelf: true, emitEvent: false });

    // Restore values if they were changed during validation
    if (etaCtrl && etaCtrl.value !== currentEta) {
      etaCtrl.setValue(currentEta!, { emitEvent: false });
    }
    if (etdCtrl && etdCtrl.value !== currentEtd) {
      etdCtrl.setValue(currentEtd!, { emitEvent: false });
    }
  }

  private updateFormControlsDisabledState(): void {
    const controlsToManage = [
      'approvals',
      'vesselId',
      'inboundVoyageId',
      'outboundVoyageId',
    ];

    // Enable or disable all controls based on hasCompletedRequests flag
    controlsToManage.forEach((controlName) => {
      const control = this.form.get(controlName);
      if (control) {
        if (this.hasCompletedRequests) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    });
  }
}
