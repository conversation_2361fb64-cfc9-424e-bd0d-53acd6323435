import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import moment from 'moment';
import { CommonModule } from '@angular/common';
import {
  WeekService,
  ScheduleModule,
  EventSettingsModel,
  TimeScaleModel,
  PopupOpenEventArgs,
  ScheduleComponent,
  NavigatingEventArgs,
} from '@syncfusion/ej2-angular-schedule';
import { SailingRequest } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request.interface';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { RoleCheckService } from 'libs/services/src/lib/services/shared/role-check.service';
import { Subscription } from 'rxjs';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { NavigationStart, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { LookaheadActions } from 'libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import {
  getStartOfWeek,
  getEndOfWeek,
  formatDate,
} from 'libs/components/src/lib/functions/utility.functions';
import { determineCardColour } from 'libs/components/src/lib/functions/card-colour.functions';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { RemoveSecondsPipe } from 'libs/components/src/lib/pipes';

@Component({
  selector: 'lookahead-scheduler',
  standalone: true,
  templateUrl: './scheduler.component.html',
  styleUrls: ['./scheduler.component.scss'],
  imports: [ScheduleModule, CommonModule, ProgressBarModule, ToastModule],
  providers: [WeekService, RemoveSecondsPipe],
})
export class SchedulerComponent implements OnInit, OnDestroy {
  @ViewChild(ScheduleComponent, { static: false })
  scheduleComponent!: ScheduleComponent;
  router = inject(Router);
  roleCheckService = inject(RoleCheckService);
  sharedService = inject(LookaheadSharedService);
  store = inject(Store);
  removeSecondsPipe = inject(RemoveSecondsPipe);
  timeScaleOptions: TimeScaleModel = { enable: false };
  weekFirstDay: number = 1;
  @Input() scheduledEventSettings: EventSettingsModel = { dataSource: [] };
  @Input() requestedEventSettings: EventSettingsModel = { dataSource: [] };
  @Input() currentUser: User | undefined;
  @Input() locationPlanMode: number | null = null;
  @Input() selectedTabIndex = 0;
  @Output() openEditSailingRequestEmit = new EventEmitter<SailingRequest>();
  private subscriptions = new Subscription();
  public flag = true;
  public selectedDate: Date = new Date();
  voyageStatus = VoyageStatus;

  ngOnInit(): void {
    this.subscriptions.add(
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationStart) {
          if (!event.url.includes('/lookahead')) {
            localStorage.removeItem('weekStartDate');
          }
        }
      })
    );

    this.subscriptions.add(
      this.sharedService.schedulerRefreshSubject.subscribe(() => {
        setTimeout(() => {
          if (this.scheduleComponent) {
            this.scheduleComponent.refresh();
          }
        }, 300);
      })
    );
    const storedDate = localStorage.getItem('weekStartDate');

    if (storedDate) {
      this.selectedDate = new Date(storedDate);
    } else {
      this.selectedDate = new Date();
    }
    window.addEventListener('beforeunload', this.onBeforeUnload);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    window.removeEventListener('beforeunload', this.onBeforeUnload);
  }

  onBeforeUnload(): void {
    localStorage.removeItem('weekStartDate');
  }

  showScheduledSailingRequests(data: any) {
    if (
      this.locationPlanMode === 0 ||
      (this.locationPlanMode === 1 &&
        data.VesselId !== null &&
        (data.InboundVoyageId !== null || data.OutboundVoyageId !== null))
    ) {
      return true;
    }
    return false;
  }

  formatTime(time: string): string {
    return moment(time, 'HH:mm:ss').format('HH:mm');
  }

  openEditSailingRequest(data: SailingRequest) {
    this.openEditSailingRequestEmit.emit(data);
  }

  onPopupOpen(args: PopupOpenEventArgs): void {
    const data = args.data;
    args.cancel = true;
    let isEmptyCell =
      args.target!.classList.contains('e-work-cells') ||
      args.target!.classList.contains('e-header-cells'); // checking whether the cell is empty or not
    const clientCanEdit =
      this.roleCheckService.hasOnlyCliRoleInPlan(this.currentUser) &&
      data &&
      data['Type'] === 0 &&
      this.currentUser!.clientId == data['ClientId'];
    if (this.roleCheckService.hasOnlyCliRoleInPlan(this.currentUser)) {
      if (!clientCanEdit) {
        return;
      }
    }
    if (isEmptyCell) {
      return;
    }

    (args.data!['Eta'] = this.removeSecondsPipe.transform(args.data!['Eta'])),
      (args.data!['Etd'] = this.removeSecondsPipe.transform(args.data!['Etd']));
    this.openEditSailingRequest(args.data as SailingRequest);
  }

  onDateChange(event: NavigatingEventArgs): void {
    const selectedDate = event.currentDate;
    const startDate = getStartOfWeek(selectedDate!);
    const endDate = getEndOfWeek(selectedDate!);

    localStorage.setItem('weekStartDate', formatDate(startDate));
    localStorage.setItem('weekEndDate', formatDate(endDate));
    this.store.dispatch(
      LookaheadActions.load_Sailing_Request_Lists({
        locationId: this.currentUser!.locationId!,
      })
    );
  }

  ShowNotification(data: any): boolean {
    if (data.SailingRequestUserComments.length) {
      const lastComment =
        data?.SailingRequestUserComments?.[
          data.SailingRequestUserComments.length - 1
        ];
      const lastCommentIsByCurrentUser =
        lastComment.createdByUserId === this.currentUser?.userId;
      if (lastCommentIsByCurrentUser) {
        return false;
      } else if (
        lastComment?.commentReadByUsers?.some(
          (x: any) => x.readerUserId == this.currentUser?.userId
        )
      ) {
        return false;
      } else {
        return true;
      }
    }
    return false;
  }

  onDataBound(args: any) {
    if (this.flag) {
      let datePicker = document.querySelector(
        '.e-schedule .e-date-range'
      ) as any;
      datePicker.click();
      datePicker.click();
      setTimeout(() => {
        var styleValue = datePicker.getBoundingClientRect();
        let headerPopup = document.querySelector(
          '.e-schedule .e-header-popup '
        ) as any;
        headerPopup.style.left = styleValue.left + 'px';
        headerPopup.style.top = styleValue.bottom + 'px';
        this.flag = false;
      }, 1000);
    }
  }

  getStyle(duration: string) {
    let style = {};
    switch (duration) {
      case 'indicator-1-day':
        style = {
          'background-color': '#DFD8FD',
          color: '#5E4DB2',
        };
        break;
      case 'indicator-2-days':
        style = {
          'background-color': '#FFEBB4',
          color: '#80620D',
        };
        break;
      case 'indicator-3-days':
        style = {
          'background-color': '#C2EBFC',
          color: '#457791',
        };
        break;
      default:
        style = {
          'background-color': 'initial',
          color: 'initial',
        };
        break;
    }
    return style;
  }

  isDryOrWetBulk(data: any): boolean {
    if (
      data.SailingRequestActivities.some(
        (x: any) =>
          x.activityCategoryTypeActivityCategoryActivityType === 0 ||
          x.activityCategoryTypeActivityCategoryActivityType === 1
      )
    ) {
      return true;
    }
    return false;
  }

  determineCardColour(args: any): void {
    determineCardColour(args);
  }
}
