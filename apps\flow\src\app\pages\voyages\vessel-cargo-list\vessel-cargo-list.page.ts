import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  Ng<PERSON>orO<PERSON>,
  NgIf,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';

import { CardModule } from 'primeng/card';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';

import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { VesselCargoListPageActions } from '../../../store/actions/vessel-cargo-list-page.actions';
import { vesselCargoListPageFeature } from '../../../store/features/vessel-cargo-list-page.feature';
import { LoaderComponent } from 'libs/components/src/lib/components/loader/loader.component';
import {
  VesselCargoListTableFields,
  VoyageCargoCustomsApprovedToLoad,
} from '../../../shared/enums';
import { RtRob } from 'libs/components/src/lib/enums/rt-rob.enum';
import { flowVoyageFeature } from '../../../store/features/flow-voyage-actions.feature';
import { FlowVoyageActions } from '../../../store/actions/flow-voyage-actions.actions';
import { AttachFilesDialogComponent } from 'libs/components/src/lib/components/attach-files-dialog/attachfiles-dialog.component';
import { FlowActions } from 'libs/services/src/lib/services/voyages/store/actions/flow.actions';
import { DepartureEmailDialogComponent } from '../departure-email/departure-email.component';
import { DiscrepancyReportEmailDialogComponent } from '../discrepancy-report-email/discrepancy-report-email.component';
import { CargoListEditActions } from 'libs/services/src/lib/services/voyages/store/actions/cargo-list-edit.actions';
import { ReportTypes } from 'libs/services/src/lib/services/voyages/enums/report-types.enum';
import { ToastService } from 'libs/components/src/lib/services/toast.service';

@Component({
  standalone: true,
  selector: 'flow-vessel-cargo-list',
  templateUrl: './vessel-cargo-list.page.html',
  styleUrls: ['./vessel-cargo-list.page.scss'],
  imports: [
    NgIf,
    BreadcrumbModule,
    CardModule,
    DropdownModule,
    MenuModule,
    ReactiveFormsModule,
    TableModule,
    NgForOf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    LoaderComponent,
    AttachFilesDialogComponent,
    DepartureEmailDialogComponent,
    DiscrepancyReportEmailDialogComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VesselCargoListPage implements OnInit {
  @Input() voyageId = '';

  private readonly store = inject(Store);
  private readonly route = inject(ActivatedRoute);
  private readonly toastService = inject(ToastService);
  canManage = this.route.snapshot.data['canManage'];
  readonly voyageDirection = VoyageDirection;
  readonly tableFields = VesselCargoListTableFields;
  readonly customStatusEnum = VoyageCargoCustomsApprovedToLoad;
  readonly rtRob = RtRob;
  readonly tableWidth = 1650;

  voyageData = this.store.selectSignal(flowFeature.selectVoyage);
  assets = this.store.selectSignal(vesselCargoListPageFeature.selectAssets);
  cargoList = this.store.selectSignal(
    vesselCargoListPageFeature.selectFilteredCargoList
  );
  loadings = this.store.selectSignal(vesselCargoListPageFeature.selectLoadings);
  columns = this.store.selectSignal(vesselCargoListPageFeature.selectColumns);
  hasReportDeckPlan = this.store.selectSignal(
    vesselCargoListPageFeature.selectHasReportDeckPlan
  );
  hasReportDiscrepancy = this.store.selectSignal(
    vesselCargoListPageFeature.selectHasReportDiscrepancy
  );
  assetsFilterControl = new FormControl<string>('All');

  attachments = this.store.selectSignal(
    flowVoyageFeature.selectFlowVoyageFiles
  );
  attachmentsLoading = this.store.selectSignal(flowVoyageFeature.selectLoading);
  isOpenAttachFilesDialog = false;
  voyageStatus = VoyageStatus;

  emailButtonVisible = computed(() => {
    return this.voyageData()?.voyageDirection === this.voyageDirection.Outbound &&
      (this.voyageData()?.voyageStatus === this.voyageStatus.Loaded ||
        this.voyageData()?.voyageStatus === this.voyageStatus.Completed);
  });

  breadcrumb = computed<MenuItem[]>(() => {
    return [
      {
        label:
          this.voyageData()?.voyageStatus === VoyageStatus.Completed
            ? 'Completed'
            : 'Active',
        routerLink:
          this.voyageData()?.voyageStatus === VoyageStatus.Completed
            ? '/voyage/completed'
            : '/voyage/active',
        styleClass: 'active',
      },
      {
        label: this.voyageData()?.vesselName,
        routerLink: `/voyage/${this.voyageData()?.voyageId}`,
        styleClass: 'active',
      },
      {
        label:
          this.voyageData()?.voyageDirection === VoyageDirection.Inbound
            ? 'Vessel Discharge'
            : 'Vessel Loadout',
      },
    ];
  });

  deckPlanItems = computed<MenuItem[]>(() => {
    return [
      {
        label: this.hasReportDeckPlan() ? 'Regenerate' : 'Generate',
        icon: 'pi pi-file',
        command: () =>
          this.store.dispatch(
            VesselCargoListPageActions.generate_Report({
              voyageId: this.voyageId,
              reportTypes: ReportTypes.DeckPlan,
            })
          ),
        disabled: !this.canManage,
      },
      {
        label: 'View',
        icon: 'pi pi-eye',
        disabled: !this.hasReportDeckPlan(),
        command: () => this.navigatePDF(),
      },
    ];
  });

  discrepancyMenuItems = computed<MenuItem[]>(() => {
    return [
      {
        label: this.hasReportDiscrepancy() ? 'Regenerate' : 'Generate',
        icon: 'pi pi-file',
        command: () =>
          this.store.dispatch(
            VesselCargoListPageActions.generate_Report({
              voyageId: this.voyageId,
              reportTypes: ReportTypes.Discrepancy,
            })
          ),
        disabled: !this.canManage,
      },
      {
        label: 'View',
        icon: 'pi pi-eye',
        disabled: !this.hasReportDiscrepancy(),
        command: () => this.navigatePDF(),
      },
      ...(this.voyageData()?.voyageDirection === VoyageDirection.Inbound
        ? [
            {
              label: 'Send via email',
              icon: 'pi pi-envelope',
              command: () => this.sendDiscrepancyReportEmail(),
              disabled: !this.canManage,
            },
          ]
        : []),
    ];
  });

   attachmentTypes = computed(() => {
    return this.attachments().map((attachment) => attachment.attachmentType);
  });

  ngOnInit() {
    var reportTypeInitial =
      this.voyageData()?.voyageDirection === VoyageDirection.Inbound
        ? ReportTypes.Discrepancy
        : ReportTypes.DeckPlan;
    this.store.dispatch(
      VesselCargoListPageActions.initialize_Page({
        voyageId: this.voyageId,
        reportTypes: reportTypeInitial,
      })
    );
  }

  sendDiscrepancyReportEmail() {
    this.store.dispatch(
      FlowActions.open_send_discrepancy_report_email_dialog({
        voyageId: this.voyageData()?.voyageId!,
        voyageNumber: this.voyageData()?.voyageNumber!,
      })
    );
  }

  navigatePDF() {
    const reportUrl =
      this.voyageData()?.voyageDirection === VoyageDirection.Inbound
        ? 'discrepancy-report-pdf'
        : 'deck-plan-report-pdf';

    window.open(`voyage/${this.voyageId}/${reportUrl}`, '_blank');
  }

  setFilter() {
    this.store.dispatch(
      VesselCargoListPageActions.filter_List_By_Asset({
        assetId: this.assetsFilterControl.value!,
      })
    );
  }

  dialogToggle() {
    this.isOpenAttachFilesDialog = !this.isOpenAttachFilesDialog;
    if (this.isOpenAttachFilesDialog) {
      this.store.dispatch(
        FlowVoyageActions.load_Flow_Voyage_Action_Attachments({
          flowVoyageActionId: this.voyageData()!.voyageId,
        })
      );
    }
  }

  removeFile(file: any) {
    this.store.dispatch(
      FlowVoyageActions.remove_Attachment({
        blobId: file.blobId,
        flowVoyageActionId: file.voyageId,
      })
    );
  }

  downloadSelectedFile(file: any) {
    this.store.dispatch(
      FlowVoyageActions.download_Attachment({
        blobId: file.blobId,
        flowVoyageActionId: file.voyageId,
      })
    );
  }

  uploadFileBulk(files: File[], voyageId: string) {
    const filteredFiles = files.filter((file) => {
      const isLargeFile = file.size > 2e7;
      if (isLargeFile) {
        this.toastService.error(`${file.name} exceeds 20MB`);
      }
      return !isLargeFile;
    });

    const formData = new FormData();
    if (filteredFiles.length && voyageId) {
      filteredFiles.forEach((file) => {
        formData.append('Attachments', file);
      });
      this.store.dispatch(
        FlowVoyageActions.upload_Files_Bulk({
          model: formData,
          flowVoyageActionId: voyageId,
        })
      );
    }
  }

  export() {
    this.store.dispatch(
      CargoListEditActions.get_Voyage_Cargo_Report({
        voyageId: this.voyageData()?.voyageId!,
        vesselName: this.voyageData()?.vesselName!,
      })
    );
  }

  sendDepartureEmail() {
    this.store.dispatch(
      FlowVoyageActions.load_Flow_Voyage_Action_Attachments({
        flowVoyageActionId: this.voyageData()?.voyageId!
      })
    );
    
    this.store.dispatch(
      FlowActions.open_send_departure_email_dialog({
        voyageId: this.voyageData()?.voyageId!,
        voyageNumber: this.voyageData()?.voyageNumber!,
      })
    );
  }
}
