import { vesselsInitialState } from './vessels.state';
import { HireStatementState } from '../../interfaces/hire-statement-state.interface';

export const hireStatementInitialState: HireStatementState = {
  hireStatements: [],
  hireStatement: null,
  isVisibleAddEditHireStatement: false,
  hireStatementBulks: [],
  hireStatementBulk: null,
  isVisibleAddEditHireStatementBulk: false,
  ...vesselsInitialState,
  loading: {
    list: false,
    createEdit: false,
    bulkList: false,
    export: false,
    exportBulks: false,
  },
};
