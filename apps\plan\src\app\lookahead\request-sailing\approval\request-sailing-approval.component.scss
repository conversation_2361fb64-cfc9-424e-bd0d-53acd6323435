.row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  margin-top: 10px;

  label {
    width: 80px;
    margin-right: 16px;
    display: flex;
    align-items: center;
  }
}

.required {
  color: red;
}
.field {
  width: 100%;
  margin-right: 16px;
  margin-top: 5px;
  line-height: 20px;
  padding-top: 0;
  padding-bottom: 0;
}
.button-container {
  padding: 16px;
  border-radius: 8px;
  max-width: 400px;
  background-color: white;

  .select-voyage {
    width: 100%;
  }

  .voyage-buttons {
    display: flex;
    margin-top: 16px;
    margin-right: 16px;

    button {
      width: 71px;
      height: 71px;
      border-radius: 4px;
      margin-right: 40px;
      background: white;
      border: 0;
      cursor: pointer;

      img {
        margin-left: -8px;
      }
    }
  }
}
.add-ons {
  display: flex;
  align-items: center;

  .addon-btn {
    border: none;
    background-color: transparent;
    cursor: pointer;

    .image-container {
      position: relative;
      width: 72px;
      height: 71px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }

      .unchecked {
        z-index: 1;
      }

      .checked,
      .hover-image {
        display: none;
        z-index: 2;
      }

      &.hover .hover-image {
        display: block;
      }

      &.active .checked {
        display: block;
      }

      &.active.hover .hover-image {
        display: none;
      }

      &.hover .unchecked {
        display: none;
      }
    }
  }
}

.image-container.disabled,
.image-container[disabled] {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: all !important;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.date-time-container {
  color: #475467;
  background-color: white;
  padding: 12px;
  border-radius: 8px;

  .header-container {
    border-bottom: 1px solid #e7e7ec;
    padding-left: 4px;
  }

  .header {
    font-weight: 400;
    display: flex;
    align-items: center;
    padding-bottom: 12px;

    .header-icon {
      font-size: 20px;
      color: #76788d;
      margin-right: 6px;
    }
  }

  .row {
    label {
      width: 80px;
      margin-right: 16px;
      display: flex;
      align-items: center;

      .required {
        color: red;
      }
    }

    .date-label {
      padding-bottom: 20px;
    }

    .input-container {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;

      input {
        padding: 8px 12px 8px 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        outline: none;
        height: 48px;
        flex-grow: 1;
      }
    }
  }
}

.comments-textarea {
  width: 100% !important;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.action-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #475467;
  transition: background-color 0.3s, color 0.3s;
}

.action-button:hover,
.action-button.active {
  background-color: rgba(0, 0, 0, 0.1);
}

.action-button:focus {
  outline: none;
  box-shadow: 0 0 3px 2px rgba(0, 0, 0, 0.2);
}

.action-container span {
  margin-top: 5px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: bold;
}

.horizontal-line {
  color: #d0d5dc;
}

.select {
  background-color: white;
  color: #00729e;
}
