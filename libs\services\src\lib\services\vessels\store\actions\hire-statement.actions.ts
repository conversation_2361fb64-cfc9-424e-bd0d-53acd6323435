import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { HireStatement } from '../../interfaces/hire-statement.interface';
import { errorProps } from '../../../../../../../components/src/lib/functions/utility.functions';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';

export const HireStatementActions = createActionGroup({
  source: 'Hire Statement',
  events: {
    load_Hire_Statements: props<{
      vesselId: string;
    }>(),
    load_Hire_Statements_Success: props<{
      hireStatements: HireStatement[];
    }>(),
    load_Hire_Statements_Failure: errorProps(),

    remove_Hire_Statement: props<{
      hireStatementId: string;
      vesselId: string;
    }>(),
    remove_Hire_Statement_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    remove_Hire_Statement_Failure: errorProps(),

    add_Hire_Statement: props<{
      hireStatement: HireStatement;
      vesselId: string;
    }>(),
    add_Hire_Statement_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    add_Hire_Statement_Failure: errorProps(),

    edit_Hire_Statement: props<{
      hireStatement: HireStatement;
      vesselId: string;
      hireStatementId: string;
    }>(),
    edit_Hire_Statement_Success: props<{
      vesselId: string;
      successMessage: string;
    }>(),
    edit_Hire_Statement_Failure: errorProps(),

    export_Hire_Statements: props<{
      vesselId: string;
    }>(),
    export_Hire_Statements_Success: emptyProps(),
    export_Hire_Statements_Failure: errorProps(),

    change_visibility_add_edit: props<{
      visible: boolean;
      hireStatement: HireStatement | null;
    }>(),
  },
});
