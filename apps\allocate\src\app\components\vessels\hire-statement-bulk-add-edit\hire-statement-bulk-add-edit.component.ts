import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgIf } from '@angular/common';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';

import { HireStatementBulk } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement-bulk.interface';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { HireStatementBulkActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement-bulk.actions';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { bulkTypesFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'lha-hire-statement-bulk-add-edit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf,
    DialogModule,
    CalendarModule,
    InputNumberModule,
    DropdownModule,
  ],
  templateUrl: './hire-statement-bulk-add-edit.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HireStatementBulkAddEditComponent {
  private readonly store = inject(Store);
  private readonly route = inject(ActivatedRoute);

  params = toSignal(this.route.params);
  hireStatementId = computed(() => {
    const value = this.params();
    return value!['hireStatementId'] as string;
  });
  isVisible = this.store.selectSignal(
    hireStatementFeature.selectIsVisibleAddEditHireStatementBulk
  );
  hireStatementBulk = this.store.selectSignal(
    hireStatementFeature.selectHireStatementBulk
  );
  hireStatements = this.store.selectSignal(
    hireStatementFeature.selectHireStatements
  );

  bulkTypes = this.store.selectSignal(bulkTypesFeature.selectBulkTypes);

  form = new FormGroup({
    bulkTypeId: new FormControl<string>('', [Validators.required]),
    startQuantity: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    price: new FormControl<number | null>(null, [
      Validators.required,
      Validators.min(0),
    ]),
    dateLoaded: new FormControl<Date | null>(null, [Validators.required]),
  });

  controls = {
    bulkTypeId: this.form.get('bulkTypeId'),
    startQuantity: this.form.get('startQuantity'),
    price: this.form.get('price'),
    dateLoaded: this.form.get('dateLoaded'),
  };

  initialValue = this.form.value;

  maxDate = computed(() => {
    const date = this.hireStatements().filter(
      (item) => item.hireStatementId === this.hireStatementId()
    )[0]?.deliveryDate;
    return new Date(date);
  });

  constructor() {
    effect(
      () => {
        if (this.isVisible()) {
          if (this.hireStatementBulk()) {
            this.form.patchValue({
              ...this.hireStatementBulk(),
              dateLoaded: this.hireStatementBulk()?.dateLoaded
                ? new Date(this.hireStatementBulk()!.dateLoaded!)
                : null,
            });
          }
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  hideDialog() {
    this.store.dispatch(
      HireStatementBulkActions.change_visibility_add_edit({
        visible: false,
        hireStatementBulk: null,
      })
    );
    this.resetForm();
  }

  resetForm() {
    this.form.reset(this.initialValue);
    this.form.updateValueAndValidity();
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      hireStatementId: this.hireStatementId(),
    } as HireStatementBulk;

    if (this.hireStatementBulk()) {
      this.store.dispatch(
        HireStatementBulkActions.edit_Hire_Statement_Bulk({
          hireStatementBulkId: this.hireStatementBulk()?.hireStatementBulkId!,
          hireStatementBulk: model,
          hireStatementId: this.hireStatementId(),
        })
      );
    } else {
      this.store.dispatch(
        HireStatementBulkActions.add_Hire_Statement_Bulk({
          hireStatementBulk: model,
          hireStatementId: this.hireStatementId(),
        })
      );
    }
  }
}
