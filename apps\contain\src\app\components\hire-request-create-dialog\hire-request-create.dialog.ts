import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { Actions } from '@ngrx/effects';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HireRequestCreate } from 'libs/services/src/lib/services/contain/interfaces/hire-request-create.interface';
import { HireRequestUnit } from 'libs/services/src/lib/services/contain/interfaces/hire-request-unit.interface';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import {
  assetsFeature,
  cargoDescriptionsFeature,
  operatorsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { TableModule } from 'primeng/table';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { HireRequestUnitsComponent } from './components/hire-request-units/hire-request-units.component';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  selector: 'contain-hire-request-create-dialog',
  standalone: true,
  imports: [
    NgIf,
    DialogModule,
    DividerModule,
    FormsModule,
    ReactiveFormsModule,
    DropdownModule,
    CheckboxModule,
    TableModule,
    InputTextareaModule,
    HireRequestUnitsComponent,
    CalendarModule,
    InputTextModule,
    InputNumberModule,
  ],
  templateUrl: './hire-request-create.dialog.html',
})
export class HireRequestCreateDialogComponent {
  @Input() dialogVisible: boolean = false;
  @Output() dialogToggle = new EventEmitter<void>();

  store = inject(Store);
  action = inject(Actions);

  assets = this.store.selectSignal(assetsFeature.selectAssets);
  clients = this.store.selectSignal(operatorsFeature.selectOperators);
  cargoDescriptions = this.store.selectSignal(
    cargoDescriptionsFeature.selectCargoDescriptions
  );
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);

  today = new Date();

  hireRequestForm = new FormGroup({
    clientId: new FormControl<string>('', Validators.required),
    assetId: new FormControl<string>('', Validators.required),
    requestedDate: new FormControl<Date | null>(
      this.today,
      Validators.required
    ),
    plannedSailingDate: new FormControl<Date | undefined>(
      undefined,
      Validators.required
    ),
    requestedBy: new FormControl<string>('', Validators.required),
    units: new FormArray([], [Validators.required, Validators.minLength(1)]),
  });

  hireRequestUnitForm = new FormGroup({
    cargoDescriptionId: new FormControl<string>('', Validators.required),
    unitQuantity: new FormControl<number | null>(1, [
      Validators.required,
      Validators.min(1),
    ]),
    cstRequired: new FormControl<boolean>(false),
    doorsRequired: new FormControl<boolean>(false),
    removableSidesRequired: new FormControl<boolean>(false),
    netRequired: new FormControl<boolean>(false),
    tarpaulinRequired: new FormControl<boolean>(false),
    shelvesRequired: new FormControl<boolean>(false),
    comments: new FormControl<string | null>(null),
    cargoDescription: new FormControl<string | null>(null),
  });

  get hireRequestUnits(): HireRequestUnit[] {
    return this.hireRequestForm.get('units')?.value as HireRequestUnit[];
  }

  hideDialog() {
    this.dialogToggle.emit();
  }

  submit(): void {
    this.hireRequestForm.markAllAsTouched();

    if (this.hireRequestForm.valid) {
      const model: HireRequestCreate = {
        clientId: this.hireRequestForm.value.clientId!,
        requestedDate: stripTimezoneOffset(
          this.hireRequestForm.value.requestedDate!
        ),
        plannedSailingDate: this.hireRequestForm.value.plannedSailingDate
          ? stripTimezoneOffset(this.hireRequestForm.value.plannedSailingDate!)
          : undefined,
        requestedBy: this.hireRequestForm.value.requestedBy!,
        units: this.hireRequestForm.value.units as HireRequestUnit[],
        assetId: this.hireRequestForm.value.assetId!,
      };

      this.store.dispatch(
        HireRequestActions.create_Hire_Request({
          hireRequest: model,
          filterModel: this.filterModel(),
        })
      );

      this.hideDialog();
    }
  }

  addUnit(): void {
    this.hireRequestUnitForm.markAllAsTouched();

    const cargoDescription = this.cargoDescriptions().find(
      (cargo) =>
        cargo.cargoDescriptionId ===
        this.hireRequestUnitForm.get('cargoDescriptionId')?.value
    );

    if (cargoDescription) {
      this.hireRequestUnitForm
        .get('cargoDescription')
        ?.setValue(cargoDescription.description);
    }

    if (this.hireRequestUnitForm.valid) {
      (this.hireRequestForm.get('units') as FormArray).push(
        this.getClonedHireRequestUnitForm()
      );

      this.hireRequestUnitForm.reset();
    }
  }

  getClonedHireRequestUnitForm(): FormGroup {
    return new FormGroup({
      cargoDescriptionId: new FormControl(
        this.hireRequestUnitForm.get('cargoDescriptionId')?.value,
        Validators.required
      ),
      unitQuantity: new FormControl(
        this.hireRequestUnitForm.get('unitQuantity')?.value,
        Validators.required
      ),
      cstRequired: new FormControl(
        this.hireRequestUnitForm.get('cstRequired')?.value ?? false
      ),
      doorsRequired: new FormControl(
        this.hireRequestUnitForm.get('doorsRequired')?.value ?? false
      ),
      removableSidesRequired: new FormControl(
        this.hireRequestUnitForm.get('removableSidesRequired')?.value ?? false
      ),
      netRequired: new FormControl(
        this.hireRequestUnitForm.get('netRequired')?.value ?? false
      ),
      tarpaulinRequired: new FormControl(
        this.hireRequestUnitForm.get('tarpaulinRequired')?.value ?? false
      ),
      shelvesRequired: new FormControl(
        this.hireRequestUnitForm.get('shelvesRequired')?.value ?? false
      ),
      plannedSailingDate: new FormControl(
        this.hireRequestUnitForm.get('plannedSailingDate')?.value ?? false
      ),
      comments: new FormControl(
        this.hireRequestUnitForm.get('comments')?.value ?? null
      ),
      cargoDescription: new FormControl(
        this.hireRequestUnitForm.get('cargoDescription')?.value ?? null
      ),
    });
  }
}
