﻿namespace Lighthouse.Model.Entity
{
    public class Vendor
    {
        public Vendor()
        {
            VendorId = Guid.NewGuid();
            CreatedDate = DateTime.UtcNow;
        }

        public Guid VendorId { get; set; }
        public Guid? VoyageId { get; set; }
        public Voyage Voyage { get; set; }
        public Guid LocationId { get; set; }
        public Location Location { get; set; }
        public string VendorName { get; set; }
        public ICollection<VendorWarehouse> VendorWarehouses { get; set; }
        public int WarehouseCount { get; set; }
        public string Address { get; set; }
        public string PostCode { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public bool Deleted { get; set; }
        public Guid CreatedById { get; set; }
        public User CreatedBy { get; set; }
        public Guid? UpdatedById { get; set; }
        public User UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool IsPeterson { get; set; } = false;
    }
}
